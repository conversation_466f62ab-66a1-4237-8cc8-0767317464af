# AlwaysOnRecorder

A React Native iOS app that implements continuous audio recording with background support and automatic file segmentation.

## Features

- **Continuous Audio Recording**: Records audio continuously with automatic file segmentation
- **Background Recording**: Maintains recording when app is backgrounded or screen is locked
- **Microphone Permission Handling**: Proper iOS permission management with `AVAudioSession.sharedInstance().requestRecordPermission`
- **Audio Session Configuration**: Configured with `.playAndRecord` category, `.mixWithOthers` and `.allowBluetooth` options
- **File Segmentation**: Automatically creates new recording files every N minutes (configurable)
- **React Native Bridge**: Exposes native functionality through `NativeModules`

## iOS Native Module Implementation

### Core Components

1. **AudioRecorderModule.swift**: Main Swift class implementing audio recording functionality
2. **AudioRecorderModule.m**: Objective-C bridge file exposing methods to React Native
3. **Info.plist**: Configured with required permissions and background modes

### Key Features Implemented

#### 1. Permissions & Audio Session
- Requests microphone permission using `AVAudioSession.sharedInstance().requestRecordPermission`
- Configures audio session with category `.playAndRecord`
- Sets options: `.mixWithOthers`, `.allowBluetooth`, `.defaultToSpeaker`
- Activates session with `.notifyOthersOnDeactivation`

#### 2. Background Recording Support
- **Info.plist Configuration**:
  - `NSMicrophoneUsageDescription`: "This app needs access to the microphone to record audio continuously."
  - `UIBackgroundModes`: `["audio"]`
- **Background Task Management**: Uses `UIApplication.shared.beginBackgroundTask` to maintain recording
- **Audio Session Interruption Handling**: Responds to phone calls and other audio interruptions

#### 3. File Segmentation
- Timer-based segmentation every N minutes (configurable)
- Automatic file naming with timestamps and segment counters
- Format: `recording_YYYYMMDD_HHMMSS_XXX.m4a`
- Seamless transition between segments (stops, renames, restarts)

#### 4. React Native Bridge Methods
- `requestMicrophonePermission()`: Requests and returns permission status
- `startRecording(segmentMinutes)`: Starts continuous recording with specified segment duration
- `stopRecording()`: Stops recording and cleans up resources
- `getNextChunkFilePath()`: Returns the path for the next recording segment
- `getCurrentRecordingInfo()`: Returns current recording status and info
- `getAllRecordingFiles()`: Lists all recorded files with metadata

## Getting Started

### Prerequisites

- React Native development environment set up
- iOS development tools (Xcode, iOS Simulator)
- Node.js and npm/yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Install iOS dependencies:
   ```bash
   cd ios && pod install && cd ..
   ```

4. Run the iOS app:
   ```bash
   npm run ios
   ```

### Testing the Implementation

Use the provided test script:
```bash
./test-ios-build.sh
```

#### Manual Testing Steps

1. **Permission Testing**:
   - Launch the app
   - Grant microphone permission when prompted
   - Verify permission status in the UI

2. **Foreground Recording**:
   - Tap "Start Recording"
   - Verify recording status shows "🔴 Recording"
   - Wait for segment duration to see file counter increment

3. **Background Recording**:
   - Start recording
   - Press home button to background the app
   - Wait for segment duration
   - Return to app and verify recording continued

4. **Screen Lock Testing**:
   - Start recording
   - Lock the device screen
   - Wait for segment duration
   - Unlock and verify recording continued

5. **File Management**:
   - Stop recording
   - Tap "Refresh Files" to see generated segments
   - Verify file names, sizes, and timestamps

## Technical Implementation Details

### Audio Configuration
```swift
try audioSession.setCategory(.playAndRecord, 
                           mode: .default, 
                           options: [.mixWithOthers, .allowBluetooth, .defaultToSpeaker])
```

### Recording Settings
```swift
let settings: [String: Any] = [
  AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
  AVSampleRateKey: 44100.0,
  AVNumberOfChannelsKey: 1,
  AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
]
```

### Background Task Management
```swift
backgroundTaskIdentifier = UIApplication.shared.beginBackgroundTask(withName: "AudioRecording") {
  self.endBackgroundTask()
}
```

## File Structure

```
ios/AlwaysOnRecorder/
├── AudioRecorderModule.swift    # Main Swift implementation
├── AudioRecorderModule.m        # Objective-C bridge
├── Info.plist                   # iOS configuration with permissions
├── AppDelegate.swift            # App delegate
└── ...

App.tsx                          # React Native UI implementation
test-ios-build.sh               # Build and test script
```

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure `NSMicrophoneUsageDescription` is set in Info.plist
2. **Background Recording Stops**: Verify `UIBackgroundModes` includes "audio"
3. **Build Errors**: Run `pod install` in the ios directory
4. **Module Not Found**: Ensure native files are added to Xcode project

### Debug Logging

The implementation includes comprehensive logging. Check Xcode console for:
- Audio session configuration status
- Recording start/stop events
- Background task lifecycle
- File creation and segmentation events

## License

This project is for demonstration purposes. Modify as needed for your use case.
