# Comprehensive Testing Workflow for Enhanced Multipart Upload

## Pre-Testing Setup

### 1. Verify S3 Connectivity
1. Launch the app
2. Press "🧪 Test S3" button
3. Check console logs for detailed test results
4. Ensure all tests pass:
   - ✅ Credentials Valid
   - ✅ Can Create Multipart
   - ✅ Can Upload Part
   - ✅ Can Complete Multipart

### 2. Check System Diagnostics
1. Press "🔍 Debug System" button
2. Verify no active sessions initially
3. Confirm S3 configuration is correct

## Test Scenario 1: Standard Multi-Chunk Recording

### Expected Behavior
- Record for ~15-20MB (3-4 chunks of 5MB each)
- Each chunk uploads immediately upon creation
- Final partial chunk handled correctly
- Single consolidated file appears in S3

### Test Steps
1. **Start Recording**
   - Press "Start Recording"
   - Verify console shows: `🚀 Starting session multipart upload for first chunk (index 0)`
   - UI should show "⚡ Multipart Upload Active"

2. **Monitor First Chunk (Index 0)**
   - Wait for first 5MB chunk
   - Console should show:
     ```
     🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
     🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 0
     🚀 [IMMEDIATE UPLOAD] Starting session multipart upload for first chunk (index 0)
     ✅ SUCCESS: Uploaded session chunk 0 as part 1
     📁 Local file preserved: [file path]
     ```
   - UI should show "Multipart Chunks: 1"

3. **Monitor Subsequent Chunks (Index 1, 2, etc.)**
   - Each additional 5MB chunk should show:
     ```
     🔍 [IMMEDIATE UPLOAD] Uploading chunk N as part N+1
     ✅ SUCCESS: Uploaded session chunk N as part N+1
     ```
   - UI should increment "Multipart Chunks" count
   - Parts display should show: "Parts: 1, 2, 3..."

4. **Stop Recording**
   - Press "Stop Recording"
   - Console should show final chunk processing:
     ```
     📦 [FINAL CHUNK] Processing final partial chunk
     🔍 [FINAL CHUNK] Is final chunk: true
     ```
   - Then completion:
     ```
     🏁 Completing session multipart upload...
     ✅ [VALIDATION] All N parts validated successfully
     ✅ [COMPLETION] Multipart upload completed successfully
     📋 Multipart manifest uploaded successfully
     ```

### Expected Results
- **S3 Bucket**: Single consolidated file `recordings/mubarak/[sessionId]/recording-[sessionId].m4a`
- **Local Storage**: All original chunk files preserved
- **UI**: Shows success message with final file location
- **Console**: No errors, all timing logs present

## Test Scenario 2: Single Chunk Recording

### Expected Behavior
- Record for <5MB (single partial chunk)
- Chunk uploads immediately
- Multipart upload completes with single part

### Test Steps
1. Record for ~30 seconds (should be <5MB)
2. Stop recording
3. Verify single part multipart upload completion

### Expected Results
- Single part in multipart upload
- Consolidated file still created correctly
- Final chunk flag handled properly

## Test Scenario 3: Local Storage Management

### Test Steps
1. **Check Initial Storage**
   - UI should show "Local Storage: 0.00 MB (0 files)"
   - "Clear Local" button should be disabled

2. **After Recording**
   - UI should show updated storage usage
   - "Clear Local" button should be enabled
   - Count should match number of chunks

3. **Clear Local Files**
   - Press "🗑️ Clear Local" button
   - Confirm deletion in alert
   - Verify storage usage resets to 0
   - Verify S3 files remain intact

## Test Scenario 4: Error Recovery

### Test Steps
1. **Simulate Network Issues**
   - Start recording
   - Disable network during upload
   - Re-enable network
   - Verify retry logic works

2. **Check Error Handling**
   - Monitor console for retry attempts
   - Verify graceful degradation
   - Check error tracking in UI

## Verification Checklist

### ✅ Immediate Upload Behavior
- [ ] First chunk triggers multipart session start
- [ ] Subsequent chunks upload immediately upon iOS event
- [ ] No waiting for recording stop to begin uploads
- [ ] Timing logs show minimal delay between event and upload start

### ✅ Final Chunk Handling
- [ ] iOS emits final chunk event with `isFinalChunk: true`
- [ ] Final partial chunk included in multipart upload
- [ ] Part numbering sequential (1, 2, 3... N)
- [ ] All parts validated before completion

### ✅ S3 Multipart Completion
- [ ] Parts validation passes (sequential numbering, valid ETags)
- [ ] Completion retry logic works if needed
- [ ] Single consolidated file appears in S3
- [ ] Manifest reflects consolidated structure

### ✅ Local File Preservation
- [ ] Original chunk files remain on device
- [ ] Local storage usage tracked accurately
- [ ] Clear local files function works
- [ ] S3 files unaffected by local cleanup

### ✅ UI Status Tracking
- [ ] Real-time upload progress shown
- [ ] Multipart session status displayed
- [ ] Local storage usage visible
- [ ] Error recovery feedback provided

## Debug Commands

### Console Commands for Troubleshooting
```javascript
// Check active multipart sessions
diagnoseMultipartSystem()

// Check session state
getSessionMultipartState('[sessionId]')

// List active uploads
getActiveSessionUploads()
```

### Key Log Patterns to Look For

**Success Pattern:**
```
🚀 Starting session multipart upload
🔄 Uploading session chunk 0 (part 1)
✅ SUCCESS: Uploaded session chunk 0 as part 1
🔄 Uploading session chunk 1 (part 2)
✅ SUCCESS: Uploaded session chunk 1 as part 2
📦 [FINAL CHUNK] Processing final partial chunk
🏁 Finishing session multipart upload
✅ [VALIDATION] All N parts validated successfully
✅ [COMPLETION] Multipart upload completed successfully
```

**Error Patterns to Investigate:**
- Missing session data errors
- Part validation failures
- AWS API errors
- Network timeout issues

## Expected File Structure

### S3 Bucket After Successful Test
```
recordings/
└── mubarak/
    └── [sessionId]/
        ├── recording-[sessionId].m4a    # Consolidated file
        └── manifest.json                # Multipart manifest
```

### Local Device After Test
```
/path/to/recordings/
├── recording_[timestamp]_001.m4a       # Preserved chunk 1
├── recording_[timestamp]_002.m4a       # Preserved chunk 2
└── recording_[timestamp]_003.m4a       # Preserved final chunk
```

This comprehensive testing workflow ensures all implemented features work correctly and provides clear verification steps for the enhanced multipart upload system.
