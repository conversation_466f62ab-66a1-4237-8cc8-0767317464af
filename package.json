{"name": "AlwaysOnRecorder", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@aws-sdk/lib-storage": "^3.850.0", "@react-native/new-app-screen": "0.80.1", "aws-sdk": "^2.1692.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "react": "19.1.0", "react-native": "0.80.1", "react-native-fs": "^2.20.0", "react-native-get-random-values": "^1.11.0", "react-native-url-polyfill": "^2.0.0", "readable-stream": "^4.7.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-class-static-block": "^7.27.1", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}