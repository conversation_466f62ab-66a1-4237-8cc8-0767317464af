# Android Audio Recording Implementation

This document describes the Android implementation of the audio recording functionality that matches the iOS version.

## Architecture Overview

The Android implementation consists of three main components:

1. **AudioRecordingService.kt** - A foreground service that handles continuous audio recording
2. **AudioRecorderModule.kt** - React Native bridge module that exposes recording functionality
3. **AudioRecorderPackage.kt** - Package registration for React Native

## Key Features Implemented

### 1. Background Recording Support
- **Foreground Service**: Uses Android's foreground service with `FOREGROUND_SERVICE_MICROPHONE` type
- **Persistent Notification**: Shows ongoing notification with recording duration and stop action
- **Wake Lock**: Prevents device from sleeping during recording using `PARTIAL_WAKE_LOCK`
- **Audio Focus Management**: Handles audio interruptions from phone calls and other apps

### 2. File Segmentation
- Timer-based segmentation every N minutes (configurable, default 5 minutes)
- Automatic file naming with timestamps and segment counters
- Format: `recording_YYYYMMDD_HHMMSS_XXX.m4a` (matches iOS format)
- Seamless transition between segments

### 3. Audio Quality Settings
- **Format**: MPEG-4 AAC (.m4a files)
- **Sample Rate**: 44.1kHz (matches iOS)
- **Channels**: Mono (1 channel)
- **Bit Rate**: 128 kbps for high quality
- **Audio Source**: Microphone input

### 4. Permissions & Manifest Configuration
```xml
<!-- Required permissions -->
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

<!-- Service declaration -->
<service
  android:name=".AudioRecordingService"
  android:enabled="true"
  android:exported="false"
  android:foregroundServiceType="microphone" />
```

### 5. Audio Focus & Interruption Handling
- **Audio Focus Request**: Requests `AUDIOFOCUS_GAIN` for recording
- **Interruption Handling**: Pauses recording during phone calls, resumes after
- **Focus Loss**: Handles temporary and permanent audio focus loss
- **Ducking Support**: Continues recording when other apps need audio (can duck)

## React Native API

The Android implementation exposes the same API methods as iOS:

```javascript
// Request microphone permission
await AudioRecorderModule.requestMicrophonePermission();

// Start recording with 5-minute segments
await AudioRecorderModule.startRecording(5);

// Stop recording
await AudioRecorderModule.stopRecording();

// Get current recording information
const info = await AudioRecorderModule.getCurrentRecordingInfo();

// Get next chunk file path
const chunk = await AudioRecorderModule.getNextChunkFilePath();

// Get all recording files
const files = await AudioRecorderModule.getAllRecordingFiles();

// Delete a recording file
await AudioRecorderModule.deleteRecordingFile(filePath);
```

## Testing Background Recording

### Basic Background Test
1. Start the app and grant microphone permission
2. Start recording
3. Press the home button to background the app
4. Wait several minutes
5. Return to the app
6. Check recording info - it should show continued recording
7. Stop recording and verify files were created

### Phone Call Interruption Test
1. Start recording
2. Make or receive a phone call
3. During the call, recording should pause
4. After ending the call, recording should resume
5. Verify the recording file contains audio before and after the call

### Battery Optimization Test
1. Start recording
2. Go to device Settings > Apps > [Your App] > Battery
3. Ensure "Battery optimization" is disabled for your app
4. Test background recording for extended periods

## File Storage

- **Location**: `Android/data/[package]/files/recordings/`
- **Format**: `.m4a` files with AAC encoding
- **Naming**: `recording_YYYYMMDD_HHMMSS_XXX.m4a`
- **Segmentation**: New file every N minutes (configurable)

## Troubleshooting

### Recording Doesn't Start
- Check microphone permission is granted
- Verify audio focus is available
- Check device audio settings
- Ensure no other app is using the microphone

### Background Recording Stops
- Check battery optimization settings
- Verify foreground service is running
- Check notification is visible
- Ensure wake lock is acquired

### Audio Quality Issues
- Verify MediaRecorder settings match requirements
- Check available storage space
- Test on different devices
- Monitor for MediaRecorder errors

### Permission Issues
- Request `RECORD_AUDIO` permission at runtime
- For Android 12+, ensure `FOREGROUND_SERVICE_MICROPHONE` is declared
- Check notification permission for persistent notifications

## Differences from iOS

1. **Service Architecture**: Android uses foreground service vs iOS background tasks
2. **Notification Requirement**: Android requires persistent notification for background recording
3. **Audio Focus**: Android has more complex audio focus system than iOS audio session
4. **Battery Optimization**: Android has additional battery optimization settings to consider
5. **Permission Model**: Android uses runtime permissions vs iOS usage descriptions

## Performance Considerations

- **Memory Usage**: Service runs in separate process, minimal impact on main app
- **Battery Usage**: Wake lock and continuous recording will impact battery
- **Storage**: Monitor available storage space for long recordings
- **CPU Usage**: AAC encoding is CPU-intensive but optimized on modern devices

## Future Enhancements

- Add support for different audio formats (WAV, MP3)
- Implement audio level monitoring
- Add recording pause/resume functionality
- Support for stereo recording
- Cloud upload integration
- Audio compression options
