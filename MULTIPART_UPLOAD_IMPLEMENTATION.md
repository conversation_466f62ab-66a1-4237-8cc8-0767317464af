# S3 Multipart Upload Implementation for AlwaysOnRecorder

## Overview

This implementation adds complete S3 multipart upload functionality to the AlwaysOnRecorder app, allowing 5MB audio chunks to be automatically consolidated into single audio files in S3 instead of remaining as separate objects.

## Key Features

### 1. Session-Based Multipart Uploads
- **Automatic Initialization**: Multipart upload starts when the first 5MB chunk is created
- **Session Tracking**: Each recording session gets its own multipart upload with unique session ID
- **Part Management**: Tracks all uploaded parts with ETags and part numbers for completion
- **Automatic Completion**: Consolidates all parts into a single S3 object when recording stops

### 2. Dual Upload Modes
- **Multipart Mode (Default)**: Consolidates chunks into single files
- **Individual Chunk Mode**: Uploads each chunk as separate S3 objects (fallback)
- **Runtime Toggle**: Users can switch modes via UI button (disabled during recording)

### 3. Enhanced Manifest System
- **Multipart Manifests**: Track consolidated files with metadata
- **Individual Chunk Manifests**: Track separate chunk files
- **Upload Method Identification**: Manifests clearly indicate upload strategy used

### 4. Error Handling & Recovery
- **Automatic Abort**: Failed multipart uploads are automatically aborted
- **Retry Logic**: Exponential backoff retry for transient failures
- **Cleanup**: Orphaned multipart uploads are cleaned up on failure
- **Fallback**: Graceful degradation to individual chunk uploads if needed

## Implementation Details

### New Functions Added

#### Core Multipart Functions
```javascript
// Start session-based multipart upload
startSessionMultipartUpload(userId, sessionId)

// Upload chunk as part of multipart upload
uploadSessionChunk(localUri, sessionId, chunkIndex, onProgress)

// Complete multipart upload and consolidate
finishSessionMultipartUpload(sessionId)

// Upload manifest for consolidated file
uploadMultipartManifest(userId, sessionId, multipartResult)

// Abort failed multipart upload
abortSessionMultipartUpload(sessionId)
```

#### State Management Functions
```javascript
// Get session upload state for debugging
getSessionMultipartState(sessionId)

// List all active session uploads
getActiveSessionUploads()

// Clear all session data (for testing)
clearAllSessionUploads()
```

### File Structure

#### S3 Object Layout
```
recordings/
├── {userId}/
│   └── {sessionId}/
│       ├── recording-{sessionId}.m4a          # Consolidated file (multipart)
│       ├── chunk-0001-{timestamp}.m4a         # Individual chunks (fallback)
│       ├── chunk-0002-{timestamp}.m4a
│       └── manifest.json                      # Session metadata
```

#### Manifest Structure (Multipart)
```json
{
  "userId": "user123",
  "sessionId": "1640995200000",
  "segmentationType": "multipart-consolidated",
  "finalFile": {
    "key": "recordings/user123/1640995200000/recording-1640995200000.m4a",
    "location": "https://bucket.s3.amazonaws.com/...",
    "etag": "\"final-etag\"",
    "totalParts": 3,
    "totalSize": 15728640
  },
  "uploadMethod": "multipart",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "completedAt": "2024-01-01T00:05:00.000Z"
}
```

### Integration Points

#### App.tsx Changes
1. **State Management**: Added multipart upload state tracking
2. **Event Handler**: Updated `handleNewAudioChunk` to support both upload modes
3. **Recording Lifecycle**: Enhanced start/stop recording to manage multipart uploads
4. **UI Updates**: Added upload mode toggle and status display

#### iOS Integration
- **No Changes Required**: Existing size-based segmentation works seamlessly
- **Event Bridge**: Uses existing `onNewAudioChunk` events
- **File Management**: Continues to create 5MB chunks as before

## Usage

### Default Behavior (Multipart Mode)
1. User starts recording
2. iOS creates 5MB chunks and emits events
3. First chunk triggers multipart upload initialization
4. Subsequent chunks are uploaded as parts
5. Recording stop triggers multipart completion
6. Result: Single consolidated audio file in S3

### Fallback Behavior (Individual Mode)
1. User toggles to individual chunk mode
2. Each 5MB chunk uploads as separate S3 object
3. Manifest tracks all individual chunks
4. Result: Multiple separate audio files in S3

### UI Controls
- **Upload Mode Toggle**: Switch between multipart and individual modes
- **Status Display**: Shows current mode and upload progress
- **Real-time Updates**: Progress tracking for active uploads

## Testing

### Comprehensive Test Suite
- **Unit Tests**: All multipart functions tested with mocked AWS SDK
- **Integration Tests**: Session lifecycle and state management
- **Error Scenarios**: Failure handling and cleanup verification
- **Edge Cases**: Empty sessions, non-existent sessions, etc.

### Test Coverage
- ✅ Multipart upload initialization
- ✅ Chunk upload as parts
- ✅ Upload completion and consolidation
- ✅ Manifest generation
- ✅ Error handling and abort
- ✅ Session state management
- ✅ Cleanup and memory management

## Benefits

### For Users
- **Simplified S3 Structure**: Single file per recording session
- **Better Organization**: Cleaner S3 bucket organization
- **Improved Performance**: Faster downloads of consolidated files
- **Cost Optimization**: Reduced S3 request costs

### For Developers
- **Maintainable Code**: Clear separation of upload strategies
- **Robust Error Handling**: Comprehensive failure recovery
- **Flexible Architecture**: Easy to extend or modify
- **Well Tested**: High confidence in reliability

## Migration Path

### Existing Recordings
- **Backward Compatibility**: Existing individual chunks remain accessible
- **No Data Loss**: All existing recordings preserved
- **Gradual Migration**: New recordings use multipart by default

### Configuration
- **Default Mode**: Multipart upload enabled by default
- **User Choice**: Toggle available for user preference
- **Runtime Switching**: Mode can be changed between recordings

## Monitoring & Debugging

### Logging
- **Detailed Console Logs**: Comprehensive logging for troubleshooting
- **State Inspection**: Functions to inspect upload state
- **Progress Tracking**: Real-time upload progress monitoring

### Error Tracking
- **Error Collection**: All upload errors tracked and displayed
- **Retry Attempts**: Automatic retry with exponential backoff
- **Failure Recovery**: Graceful handling of network issues

This implementation provides a robust, scalable solution for consolidating audio chunks into single S3 objects while maintaining backward compatibility and providing flexible upload options.
