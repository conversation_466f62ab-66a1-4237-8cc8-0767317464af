import AVFoundation
import Foundation
import React
import UIKit
import UserNotifications

@objc(AudioRecorderModule)
class AudioRecorderModule: RCTEventEmitter {

  @objc
  override static func moduleName() -> String! {
    return "AudioRecorderModule"
  }

  @objc
  override static func requiresMainQueueSetup() -> Bool {
    return false
  }

  // MARK: - RCTEventEmitter Methods
  override func supportedEvents() -> [String]! {
    return ["onNewAudioChunk", "onRecordingStarted"]
  }

  // MARK: - Properties
  private var audioRecorder: AVAudioRecorder?
  private var audioSession: AVAudioSession = AVAudioSession.sharedInstance()
  private var currentRecordingURL: URL?
  private var recordingCounter: Int = 0
  private var isRecording: Bool = false
  private var backgroundTaskIdentifier: UIBackgroundTaskIdentifier = .invalid

  // Timer properties
  private var recordingStartTime: Date?
  private var currentSegmentStartTime: Date?
  private var timerUpdateTimer: Timer?

  // Size-based segmentation properties
  private let targetChunkSizeBytes: Int64 = 5 * 1024 * 1024  // 5MB
  private var lastCheckedFileSize: Int64 = 0
  private var useSizeBasedSegmentation: Bool = false

  // Chunk finalization tracking - CRITICAL for moov atom fix
  private var pendingChunkURL: URL?
  private var pendingChunkIndex: Int = 0
  private var isWaitingForChunkFinalization: Bool = false

  // Playback properties
  private var audioPlayer: AVAudioPlayer?
  private var isPlaying: Bool = false
  private var currentPlayingFile: String?
  private var playbackTimer: Timer?

  // Notification properties
  private var notificationTimer: Timer?
  private let notificationIdentifier = "AudioRecordingNotification"

  // MARK: - Initialization
  override init() {
    super.init()
    setupAudioSession()
    setupNotificationObservers()
    requestNotificationPermission()

    // Auto-test event emission after 3 seconds to verify bridge connection
    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
      NSLog("🧪 [AudioRecorderModule] Auto-testing event emission after 3 seconds")
      self.testEventEmission(
        { result in
          NSLog("🧪 [AudioRecorderModule] Auto-test result: \(result)")
        },
        rejecter: { code, message, error in
          NSLog("🧪 [AudioRecorderModule] Auto-test failed: \(message ?? "unknown error")")
        })
    }
  }

  deinit {
    NotificationCenter.default.removeObserver(self)
    removeRecordingNotification()
  }

  // MARK: - Audio Session Setup
  private func setupAudioSession() {
    do {
      try audioSession.setCategory(
        .playAndRecord,
        mode: .default,
        options: [.mixWithOthers, .allowBluetooth, .defaultToSpeaker])
      try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
      print("Audio session configured successfully")
    } catch {
      print("Failed to configure audio session: \(error.localizedDescription)")
    }
  }

  private func setupNotificationObservers() {
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(handleAppDidEnterBackground),
      name: UIApplication.didEnterBackgroundNotification,
      object: nil
    )

    NotificationCenter.default.addObserver(
      self,
      selector: #selector(handleAppWillEnterForeground),
      name: UIApplication.willEnterForegroundNotification,
      object: nil
    )

    NotificationCenter.default.addObserver(
      self,
      selector: #selector(handleAudioSessionInterruption),
      name: AVAudioSession.interruptionNotification,
      object: nil
    )
  }

  @objc private func handleAppDidEnterBackground() {
    guard isRecording else { return }

    backgroundTaskIdentifier = UIApplication.shared.beginBackgroundTask(withName: "AudioRecording")
    {
      // Background task is about to expire
      self.endBackgroundTask()
    }

    print("App entered background, started background task: \(backgroundTaskIdentifier.rawValue)")
  }

  @objc private func handleAppWillEnterForeground() {
    endBackgroundTask()
    print("App will enter foreground")
  }

  @objc private func handleAudioSessionInterruption(notification: Notification) {
    guard let userInfo = notification.userInfo,
      let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
      let type = AVAudioSession.InterruptionType(rawValue: typeValue)
    else {
      return
    }

    switch type {
    case .began:
      print("Audio session interruption began")
    // Recording will be paused automatically
    case .ended:
      if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
        let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
        if options.contains(.shouldResume) {
          print("Audio session interruption ended, should resume")
          // Resume recording if it was active
          if isRecording && audioRecorder?.isRecording == false {
            audioRecorder?.record()
          }
        }
      }
    @unknown default:
      break
    }
  }

  private func endBackgroundTask() {
    if backgroundTaskIdentifier != .invalid {
      UIApplication.shared.endBackgroundTask(backgroundTaskIdentifier)
      backgroundTaskIdentifier = .invalid
    }
  }

  // MARK: - Permission Handling
  @objc
  func requestMicrophonePermission(
    _ resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {
    audioSession.requestRecordPermission { granted in
      DispatchQueue.main.async {
        if granted {
          resolve(["granted": true])
        } else {
          reject("PERMISSION_DENIED", "Microphone permission denied", nil)
        }
      }
    }
  }

  // MARK: - Recording Methods
  @objc
  func startRecording(
    _ resolve: @escaping RCTPromiseResolveBlock,
    reject: @escaping RCTPromiseRejectBlock
  ) {

    // CRITICAL DEBUG: Log method parameters
    NSLog("🔍 [AudioRecorderModule] startRecording called")
    NSLog("🔍 [AudioRecorderModule] Current isRecording state: \(isRecording)")

    guard !isRecording else {
      reject("ALREADY_RECORDING", "Recording is already in progress", nil)
      return
    }

    // Check microphone permission
    guard audioSession.recordPermission == .granted else {
      reject("PERMISSION_DENIED", "Microphone permission not granted", nil)
      return
    }

    recordingCounter = 0
    self.useSizeBasedSegmentation = true  // Always use size-based segmentation

    print("🎬 Starting recording with size-based segmentation: true")

    do {
      // Ensure audio session is properly configured for recording
      try audioSession.setCategory(
        .playAndRecord,
        mode: .default,
        options: [.mixWithOthers, .allowBluetooth, .defaultToSpeaker])
      try audioSession.setActive(true, options: .notifyOthersOnDeactivation)

      recordingCounter = 1

      // Initialize timer tracking
      let now = Date()
      recordingStartTime = now
      currentSegmentStartTime = now

      NSLog("🔍 [AudioRecorderModule] About to start new recording segment")
      try startNewRecordingSegment()
      NSLog("🔍 [AudioRecorderModule] Recording segment started successfully")
      isRecording = true

      NSLog(
        "🔍 [AudioRecorderModule] Size-based segmentation enabled - setting up file size monitoring")
      NSLog("🔍 [AudioRecorderModule] About to setup timer update timer")
      setupTimerUpdateTimer()
      NSLog("🔍 [AudioRecorderModule] Timer update timer setup completed")

      // Test event emission to verify React Native bridge is working
      NSLog("🧪 [AudioRecorderModule] Emitting test event to verify bridge connection")
      let testEvent: [String: Any] = [
        "message": "Recording started successfully",
        "timestamp": Int64(Date().timeIntervalSince1970 * 1000),
      ]
      self.sendEvent(withName: "onRecordingStarted", body: testEvent)
      NSLog("🧪 [AudioRecorderModule] Test event emitted")

      showRecordingNotification()
      resolve(["success": true, "filePath": currentRecordingURL?.path ?? ""])
    } catch {
      reject("RECORDING_ERROR", "Failed to start recording: \(error.localizedDescription)", error)
    }
  }

  @objc
  func stopRecording(
    _ resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {

    guard isRecording else {
      reject("NOT_RECORDING", "No recording in progress", nil)
      return
    }

    stopCurrentRecording()
    isRecording = false
    resolve(["success": true])
  }

  @objc
  func getNextChunkFilePath(
    _ resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {
    let nextFilePath = generateRecordingFilePath(counter: recordingCounter + 1)
    resolve(["filePath": nextFilePath.path])
  }

  @objc
  func getCurrentRecordingInfo(
    _ resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {
    resolve([
      "isRecording": isRecording,
      "currentFilePath": currentRecordingURL?.path ?? "",
      "recordingCounter": recordingCounter,
    ])
  }

  @objc
  func testEventEmission(
    _ resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {
    NSLog("🧪 [testEventEmission] Manual test event emission triggered")

    let testChunkInfo: [String: Any] = [
      "filePath": "/test/manual/path.m4a",
      "chunkIndex": 999,
      "fileSize": 5_242_880,
      "timestamp": Int64(Date().timeIntervalSince1970 * 1000),
    ]

    NSLog("🧪 [testEventEmission] Emitting test onNewAudioChunk event")
    self.sendEvent(withName: "onNewAudioChunk", body: testChunkInfo)
    NSLog("🧪 [testEventEmission] Test event emitted successfully")

    resolve(["success": true, "message": "Test event emitted"])
  }

  @objc
  func getAllRecordingFiles(
    _ resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {
    let documentsPath = FileManager.default.urls(
      for: .documentDirectory,
      in: .userDomainMask)[0]

    do {
      let files = try FileManager.default.contentsOfDirectory(
        at: documentsPath,
        includingPropertiesForKeys: [.creationDateKey],
        options: [])

      let recordingFiles =
        files
        .filter { $0.pathExtension == "m4a" && $0.lastPathComponent.hasPrefix("recording_") }
        .map { url -> [String: Any] in
          let attributes = try? FileManager.default.attributesOfItem(atPath: url.path)
          let fileSize = attributes?[.size] as? Int64 ?? 0
          let creationDate = attributes?[.creationDate] as? Date ?? Date()

          return [
            "filePath": url.path,
            "fileName": url.lastPathComponent,
            "fileSize": fileSize,
            "creationDate": ISO8601DateFormatter().string(from: creationDate),
          ]
        }
        .sorted { (file1, file2) -> Bool in
          let date1 =
            ISO8601DateFormatter().date(from: file1["creationDate"] as? String ?? "")
            ?? Date.distantPast
          let date2 =
            ISO8601DateFormatter().date(from: file2["creationDate"] as? String ?? "")
            ?? Date.distantPast
          return date1 < date2
        }

      resolve(["files": recordingFiles])
    } catch {
      reject("FILE_ERROR", "Failed to get recording files: \(error.localizedDescription)", error)
    }
  }

  @objc
  func clearAllRecordingFiles(
    _ resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {

    // Stop any current playback first
    audioPlayer?.stop()
    audioPlayer = nil
    playbackTimer?.invalidate()
    playbackTimer = nil

    let documentsPath = FileManager.default.urls(
      for: .documentDirectory,
      in: .userDomainMask
    ).first!

    do {
      let fileURLs = try FileManager.default.contentsOfDirectory(
        at: documentsPath,
        includingPropertiesForKeys: nil,
        options: [])

      let audioFiles = fileURLs.filter {
        $0.pathExtension == "m4a" && $0.lastPathComponent.hasPrefix("recording_")
      }
      var deletedCount = 0
      var errors: [String] = []

      for fileURL in audioFiles {
        do {
          try FileManager.default.removeItem(at: fileURL)
          deletedCount += 1
          print("Deleted file: \(fileURL.lastPathComponent)")
        } catch {
          errors.append(
            "Failed to delete \(fileURL.lastPathComponent): \(error.localizedDescription)")
        }
      }

      if errors.isEmpty {
        resolve([
          "success": true,
          "deletedCount": deletedCount,
          "message": "Successfully deleted \(deletedCount) files",
        ])
      } else {
        resolve([
          "success": false,
          "deletedCount": deletedCount,
          "errors": errors,
          "message": "Deleted \(deletedCount) files with \(errors.count) errors",
        ])
      }

    } catch {
      reject(
        "CLEAR_FILES_ERROR", "Failed to clear recording files: \(error.localizedDescription)", error
      )
    }
  }

  // MARK: - Timer Methods
  @objc
  func getRecordingTimer(
    _ resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {
    guard isRecording else {
      resolve([
        "isRecording": false,
        "totalDuration": 0,
        "segmentDuration": 0,
        "formattedTotal": "00:00",
        "formattedSegment": "00:00",
      ])
      return
    }

    let now = Date()
    let totalDuration = recordingStartTime?.timeIntervalSince(now) ?? 0
    let segmentDuration = currentSegmentStartTime?.timeIntervalSince(now) ?? 0

    resolve([
      "isRecording": true,
      "totalDuration": abs(totalDuration),
      "segmentDuration": abs(segmentDuration),
      "formattedTotal": formatDuration(abs(totalDuration)),
      "formattedSegment": formatDuration(abs(segmentDuration)),
    ])
  }

  private func formatDuration(_ duration: TimeInterval) -> String {
    let hours = Int(duration) / 3600
    let minutes = Int(duration) % 3600 / 60
    let seconds = Int(duration) % 60

    if hours > 0 {
      return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    } else {
      return String(format: "%02d:%02d", minutes, seconds)
    }
  }

  // MARK: - Playback Methods
  @objc
  func playRecording(
    _ filePath: String,
    resolver resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {

    // Stop any current playback
    stopCurrentPlayback()

    let fileURL = URL(fileURLWithPath: filePath)

    guard FileManager.default.fileExists(atPath: filePath) else {
      reject("FILE_NOT_FOUND", "Audio file not found at path: \(filePath)", nil)
      return
    }

    do {
      // Configure audio session for playback
      try audioSession.setCategory(.playback, mode: .default, options: [])
      try audioSession.setActive(true)

      audioPlayer = try AVAudioPlayer(contentsOf: fileURL)
      audioPlayer?.delegate = self
      audioPlayer?.prepareToPlay()

      guard audioPlayer?.play() == true else {
        reject("PLAYBACK_ERROR", "Failed to start playback", nil)
        return
      }

      isPlaying = true
      currentPlayingFile = filePath
      setupPlaybackTimer()

      resolve([
        "success": true,
        "duration": audioPlayer?.duration ?? 0,
        "filePath": filePath,
      ])

    } catch {
      reject(
        "PLAYBACK_ERROR", "Failed to initialize audio player: \(error.localizedDescription)", error)
    }
  }

  @objc
  func stopPlayback(
    _ resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {
    stopCurrentPlayback()
    resolve(["success": true])
  }

  @objc
  func getPlaybackStatus(
    _ resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {
    guard let player = audioPlayer else {
      resolve([
        "isPlaying": false,
        "currentTime": 0,
        "duration": 0,
        "filePath": "",
        "progress": 0,
      ])
      return
    }

    let progress = player.duration > 0 ? player.currentTime / player.duration : 0

    resolve([
      "isPlaying": isPlaying && player.isPlaying,
      "currentTime": player.currentTime,
      "duration": player.duration,
      "filePath": currentPlayingFile ?? "",
      "progress": progress,
      "formattedCurrentTime": formatDuration(player.currentTime),
      "formattedDuration": formatDuration(player.duration),
    ])
  }

  private func stopCurrentPlayback() {
    playbackTimer?.invalidate()
    playbackTimer = nil
    audioPlayer?.stop()
    audioPlayer = nil
    isPlaying = false
    currentPlayingFile = nil

    // Restore audio session for recording if needed
    if isRecording {
      do {
        try audioSession.setCategory(
          .playAndRecord,
          mode: .default,
          options: [.mixWithOthers, .allowBluetooth, .defaultToSpeaker])
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
      } catch {
        print("Failed to restore recording audio session: \(error.localizedDescription)")
      }
    }
  }

  private func setupPlaybackTimer() {
    playbackTimer?.invalidate()
    playbackTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
      // Timer for smooth playback progress updates
      // React Native will poll getPlaybackStatus for updates
    }
  }

  // MARK: - Notification Methods
  private func requestNotificationPermission() {
    UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) {
      granted, error in
      if let error = error {
        print("Notification permission error: \(error.localizedDescription)")
      } else {
        print("Notification permission granted: \(granted)")
      }
    }
  }

  private func showRecordingNotification() {
    let content = UNMutableNotificationContent()
    content.title = "Audio Recording in Progress"
    content.subtitle = "Recording started"
    content.sound = nil  // Silent notification
    content.categoryIdentifier = "RECORDING_CATEGORY"

    // Create a trigger that fires immediately and doesn't repeat
    let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 0.1, repeats: false)

    let request = UNNotificationRequest(
      identifier: notificationIdentifier,
      content: content,
      trigger: trigger)

    UNUserNotificationCenter.current().add(request) { error in
      if let error = error {
        print("Failed to show recording notification: \(error.localizedDescription)")
      } else {
        print("Recording notification shown")
      }
    }

    // Start timer to update notification
    setupNotificationTimer()
  }

  private func updateRecordingNotification() {
    guard isRecording, let startTime = recordingStartTime else { return }

    let duration = Date().timeIntervalSince(startTime)
    let formattedDuration = formatDuration(duration)

    let content = UNMutableNotificationContent()
    content.title = "Audio Recording in Progress"
    content.subtitle = "Recording time: \(formattedDuration)"
    content.sound = nil
    content.categoryIdentifier = "RECORDING_CATEGORY"

    let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 0.1, repeats: false)
    let request = UNNotificationRequest(
      identifier: notificationIdentifier,
      content: content,
      trigger: trigger)

    UNUserNotificationCenter.current().add(request) { error in
      if let error = error {
        print("Failed to update recording notification: \(error.localizedDescription)")
      }
    }
  }

  private func removeRecordingNotification() {
    notificationTimer?.invalidate()
    notificationTimer = nil
    UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [
      notificationIdentifier
    ])
    UNUserNotificationCenter.current().removeDeliveredNotifications(withIdentifiers: [
      notificationIdentifier
    ])
    print("Recording notification removed")
  }

  private func setupNotificationTimer() {
    notificationTimer?.invalidate()
    notificationTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) {
      [weak self] _ in
      self?.updateRecordingNotification()
    }
  }

  // MARK: - Private Recording Methods
  private func startNewRecordingSegment() throws {
    // Stop current recording if exists
    audioRecorder?.stop()

    // Generate new file path
    currentRecordingURL = generateRecordingFilePath(counter: recordingCounter)

    guard let recordingURL = currentRecordingURL else {
      throw NSError(
        domain: "AudioRecorderModule",
        code: -1,
        userInfo: [NSLocalizedDescriptionKey: "Failed to generate recording file path"])
    }

    // Configure recording settings
    let settings: [String: Any] = [
      AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
      AVSampleRateKey: 44100.0,
      AVNumberOfChannelsKey: 1,
      AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue,
    ]

    // Create and start new recorder
    do {
      audioRecorder = try AVAudioRecorder(url: recordingURL, settings: settings)
      audioRecorder?.delegate = self
      audioRecorder?.isMeteringEnabled = true

      guard audioRecorder?.record() == true else {
        throw NSError(
          domain: "AudioRecorderModule",
          code: -2,
          userInfo: [
            NSLocalizedDescriptionKey:
              "AVAudioRecorder failed to start recording. Check audio session and permissions."
          ])
      }

      NSLog(
        "🎬 [startNewRecordingSegment] Started recording segment \(recordingCounter) to: \(recordingURL.path)"
      )
      NSLog(
        "🎬 [startNewRecordingSegment] Recorder is recording: \(audioRecorder?.isRecording ?? false)"
      )
      NSLog(
        "🎬 [startNewRecordingSegment] File exists: \(FileManager.default.fileExists(atPath: recordingURL.path))"
      )
    } catch let error as NSError {
      print("Failed to create or start AVAudioRecorder: \(error.localizedDescription)")
      throw error
    }
  }

  private func stopCurrentRecording() {
    timerUpdateTimer?.invalidate()
    timerUpdateTimer = nil

    // Handle final partial chunk before stopping
    if let currentURL = currentRecordingURL, isRecording {
      do {
        // Get final file size
        let attributes = try FileManager.default.attributesOfItem(atPath: currentURL.path)
        let finalFileSize = attributes[.size] as? Int64 ?? 0

        print("📦 [FINAL CHUNK] Processing final partial chunk with size: \(finalFileSize) bytes")

        // Only process final chunk if there's meaningful content (> 1KB)
        if finalFileSize > 1024 {
          // Store pending final chunk info for delegate callback
          pendingChunkURL = currentURL
          pendingChunkIndex = recordingCounter - 1  // Current chunk index (0-based)
          isWaitingForChunkFinalization = true

          // Stop recording to finalize the file - delegate will handle the rest
          print("🛑 [FINAL CHUNK] Stopping recorder to finalize final chunk moov atom...")
          audioRecorder?.stop()

          // NOTE: We'll emit the final chunk event in audioRecorderDidFinishRecording
          // to ensure the moov atom is written
        } else {
          print("📦 [FINAL CHUNK] Final chunk too small (\(finalFileSize) bytes), skipping")
          audioRecorder?.stop()
          finishStoppingRecording()
        }
      } catch {
        print("⚠️ [FINAL CHUNK] Failed to get final chunk size: \(error.localizedDescription)")
        audioRecorder?.stop()
        finishStoppingRecording()
      }
    } else {
      audioRecorder?.stop()
      finishStoppingRecording()
    }
  }

  private func finishStoppingRecording() {
    audioRecorder = nil
    endBackgroundTask()
    removeRecordingNotification()

    // Reset timer tracking
    recordingStartTime = nil
    currentSegmentStartTime = nil

    // Reset chunk finalization state
    pendingChunkURL = nil
    pendingChunkIndex = 0
    isWaitingForChunkFinalization = false

    print("Stopped recording")
  }

  private func setupTimerUpdateTimer() {
    NSLog("🔍 [AudioRecorderModule] Setting up file size monitoring timer")
    timerUpdateTimer?.invalidate()

    // Create timer for size-based segmentation monitoring
    timerUpdateTimer = Timer(timeInterval: 1.0, repeats: true) { [weak self] timer in
      NSLog("⏰ [Timer] Timer fired - checking file size")
      // Check file size for size-based segmentation
      if self?.isRecording == true {
        NSLog("⏰ [Timer] Recording is active, calling checkFileSizeForSegmentation")
        self?.checkFileSizeForSegmentation()
      } else {
        NSLog("⏰ [Timer] Recording is not active, skipping file size check")
      }
    }

    // Add timer to run loop to ensure it runs in background
    if let timer = timerUpdateTimer {
      RunLoop.main.add(timer, forMode: .common)
      NSLog("🔍 [AudioRecorderModule] File size monitoring timer scheduled and added to run loop")
      NSLog("🔍 [AudioRecorderModule] Timer valid: \(timer.isValid), fire date: \(timer.fireDate)")
    } else {
      NSLog("❌ [AudioRecorderModule] Failed to create timer")
    }
  }

  private func generateRecordingFilePath(counter: Int) -> URL {
    let documentsPath = FileManager.default.urls(
      for: .documentDirectory,
      in: .userDomainMask)[0]
    let timestamp = DateFormatter().apply {
      $0.dateFormat = "yyyyMMdd_HHmmss"
    }.string(from: Date())

    let fileName = "recording_\(timestamp)_\(String(format: "%03d", counter)).m4a"
    return documentsPath.appendingPathComponent(fileName)
  }

  // MARK: - Size-based Segmentation Methods
  private func checkFileSizeForSegmentation() {
    guard isRecording, let recordingURL = currentRecordingURL else {
      print(
        "🔍 [checkFileSizeForSegmentation] Guard failed - isRecording: \(isRecording), recordingURL: \(currentRecordingURL?.path ?? "nil")"
      )
      return
    }

    do {
      let attributes = try FileManager.default.attributesOfItem(atPath: recordingURL.path)
      let currentFileSize = attributes[.size] as? Int64 ?? 0
      let currentSizeMB = Double(currentFileSize) / 1024.0 / 1024.0
      let targetSizeMB = Double(targetChunkSizeBytes) / 1024.0 / 1024.0

      // Enhanced logging - show every check for debugging
      NSLog(
        "🔍 [checkFileSizeForSegmentation] Current: \(String(format: "%.2f", currentSizeMB))MB, Target: \(String(format: "%.2f", targetSizeMB))MB, Last checked: \(String(format: "%.2f", Double(lastCheckedFileSize) / 1024.0 / 1024.0))MB"
      )

      // Only check if file size has grown significantly (avoid constant checking)
      if currentFileSize > lastCheckedFileSize + (1024 * 1024) {  // Check every 1MB growth
        lastCheckedFileSize = currentFileSize

        NSLog("📏 File size milestone reached: \(String(format: "%.2f", currentSizeMB)) MB")

        // If file size reaches target, trigger segmentation
        if currentFileSize >= targetChunkSizeBytes {
          NSLog(
            "🎯 [checkFileSizeForSegmentation] File size reached target (\(targetChunkSizeBytes) bytes), triggering segmentation"
          )
          NSLog("🎯 [checkFileSizeForSegmentation] Current file: \(recordingURL.path)")
          NSLog("🎯 [checkFileSizeForSegmentation] About to call triggerSizeBasedSegmentation()")
          triggerSizeBasedSegmentation()
          NSLog("🎯 [checkFileSizeForSegmentation] triggerSizeBasedSegmentation() completed")
        } else {
          NSLog(
            "📏 [checkFileSizeForSegmentation] File size not yet at target - continuing recording (current: \(String(format: "%.2f", currentSizeMB))MB, target: \(String(format: "%.2f", targetSizeMB))MB)"
          )
        }
      }
    } catch {
      print("⚠️ Failed to check file size: \(error.localizedDescription)")
    }
  }

  private func triggerSizeBasedSegmentation() {
    guard isRecording, let completedFileURL = currentRecordingURL else { return }
    guard !isWaitingForChunkFinalization else {
      print("⚠️ Already waiting for chunk finalization, skipping")
      return
    }

    do {
      // Get file size before stopping recording
      let attributes = try FileManager.default.attributesOfItem(atPath: completedFileURL.path)
      let fileSize = attributes[.size] as? Int64 ?? 0

      print("📦 Completing chunk \(recordingCounter) with size: \(fileSize) bytes")

      // Store pending chunk info for delegate callback
      pendingChunkURL = completedFileURL
      pendingChunkIndex = recordingCounter - 1  // 0-based index for JS
      isWaitingForChunkFinalization = true

      // Stop current recording to finalize the file - this will trigger audioRecorderDidFinishRecording
      print("🛑 Stopping recorder to finalize moov atom...")
      audioRecorder?.stop()

      // NOTE: We do NOT emit the event here! We wait for audioRecorderDidFinishRecording
      // to confirm the moov atom has been written before notifying React Native

    } catch {
      print("❌ Failed to trigger size-based segmentation: \(error.localizedDescription)")
      isWaitingForChunkFinalization = false
      // Continue recording even if segmentation fails
    }
  }
}

// MARK: - AVAudioRecorderDelegate
extension AudioRecorderModule: AVAudioRecorderDelegate {
  func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
    print("audioRecorderDidFinishRecording called - successfully: \(flag)")

    guard flag else {
      print("Recording did not finish successfully")
      isWaitingForChunkFinalization = false
      return
    }

    if isWaitingForChunkFinalization, let chunkURL = pendingChunkURL {
      handleChunkFinalization(chunkURL: chunkURL, chunkIndex: pendingChunkIndex)
    }
  }

  func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
    print("Recording encode error: \(error?.localizedDescription ?? "Unknown error")")
    isWaitingForChunkFinalization = false
  }

  private func handleChunkFinalization(chunkURL: URL, chunkIndex: Int) {
    do {
      let attributes = try FileManager.default.attributesOfItem(atPath: chunkURL.path)
      let finalFileSize = attributes[.size] as? Int64 ?? 0

      print("Chunk finalized with moov atom - final size: \(finalFileSize) bytes")

      let isFinalChunk = !isRecording

      var chunkInfo: [String: Any] = [
        "filePath": chunkURL.path,
        "chunkIndex": chunkIndex,
        "fileSize": finalFileSize,
        "timestamp": Int64(Date().timeIntervalSince1970 * 1000),
        "hasMovAtom": true,
      ]

      if isFinalChunk {
        chunkInfo["isFinalChunk"] = true
      }

      NSLog("Emitting onNewAudioChunk event for finalized chunk")
      self.sendEvent(withName: "onNewAudioChunk", body: chunkInfo)
      NSLog("Event emitted - chunk is ready for S3 upload")

      print("Emitted onNewAudioChunk event for finalized chunk \(chunkIndex)")

      if isRecording {
        recordingCounter += 1
        currentSegmentStartTime = Date()
        lastCheckedFileSize = 0

        do {
          try startNewRecordingSegment()
          print("Started new recording segment: \(recordingCounter)")
        } catch {
          print("Failed to start new recording segment: \(error.localizedDescription)")
        }
      } else {
        finishStoppingRecording()
      }

    } catch {
      print("Failed to get final chunk size: \(error.localizedDescription)")
      if !isRecording {
        finishStoppingRecording()
      }
    }

    pendingChunkURL = nil
    pendingChunkIndex = 0
    isWaitingForChunkFinalization = false
  }
}

// MARK: - AVAudioPlayerDelegate
extension AudioRecorderModule: AVAudioPlayerDelegate {
  func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
    print("Playback finished successfully: \(flag)")
    stopCurrentPlayback()
  }

  func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
    print("Playback decode error: \(error?.localizedDescription ?? "Unknown error")")
    stopCurrentPlayback()
  }
}

// MARK: - Extension for DateFormatter
extension DateFormatter {
  func apply(_ closure: (DateFormatter) -> Void) -> DateFormatter {
    closure(self)
    return self
  }
}
