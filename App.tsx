/**
 * AlwaysOnRecorder App
 * Demonstrates continuous audio recording with React Native
 *
 * @format
 */

// Import polyfills first
import 'react-native-get-random-values';

import React, { useState, useEffect, useCallback } from 'react';
import {
  StatusBar,
  StyleSheet,
  useColorScheme,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ScrollView,
  NativeModules,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import TestAudioRecording from './TestAudioRecording';

const { AudioRecorderModule } = NativeModules;

// Import S3 upload functionality - Multipart uploads for size-based segmentation
import {
  uploadManifest,
  retryUpload,
  S3_CONFIG,
  finishMultipartUpload,
  uploadSingleChunk,
  generateSingleChunkKey,
  uploadSizeBasedManifest,
  // New multipart upload functions
  startSessionMultipartUpload,
  uploadSessionChunk,
  finishSessionMultipartUpload,
  uploadMultipartManifest,
  abortSessionMultipartUpload,
  getSessionMultipartState,
  diagnoseMultipartSystem,
  cleanupSessionUpload,
} from './src/aws/uploadChunk';
import { testCredentials, testS3Connectivity } from './src/aws/s3Client';

// Debug: Check if module is available
console.log('Available NativeModules:', Object.keys(NativeModules));
console.log('AudioRecorderModule:', AudioRecorderModule);

interface RecordingFile {
  filePath: string;
  fileName: string;
  fileSize: number;
  creationDate: string;
}

interface RecordingInfo {
  isRecording: boolean;
  currentFilePath: string;
  recordingCounter: number;
  segmentDurationMinutes: number;
}

interface RecordingTimer {
  isRecording: boolean;
  totalDuration: number;
  segmentDuration: number;
  formattedTotal: string;
  formattedSegment: string;
}

interface PlaybackStatus {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  filePath: string;
  progress: number;
  formattedCurrentTime: string;
  formattedDuration: string;
}

function App() {
  const isDarkMode = useColorScheme() === 'dark';

  // For Android, use the TestAudioRecording component
  if (Platform.OS === 'android') {
    return <TestAudioRecording />;
  }

  // iOS implementation continues below
  const [isRecording, setIsRecording] = useState(false);
  const [recordingInfo, setRecordingInfo] = useState<RecordingInfo | null>(
    null,
  );
  const [recordingFiles, setRecordingFiles] = useState<RecordingFile[]>([]);
  const [permissionGranted, setPermissionGranted] = useState(false);
  const [recordingTimer, setRecordingTimer] = useState<RecordingTimer | null>(
    null,
  );
  const [playbackStatus, setPlaybackStatus] = useState<PlaybackStatus | null>(
    null,
  );
  const [currentlyPlayingFile, setCurrentlyPlayingFile] = useState<
    string | null
  >(null);

  // S3 upload state
  const [uploadProgress, setUploadProgress] = useState<{
    [key: string]: number;
  }>({});
  const [uploadedChunks, setUploadedChunks] = useState<
    Array<{ key: string; timestamp: string; size: number }>
  >([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [uploadErrors, setUploadErrors] = useState<
    Array<{ chunkIndex: number; error: string; timestamp: string }>
  >([]);

  // Size-based upload state
  const [sizeBasedUploadedChunks, setSizeBasedUploadedChunks] = useState<
    Array<{
      chunkIndex: number;
      s3Key: string;
      timestamp: string;
      size: number;
      etag: string;
    }>
  >([]);

  // Multipart upload state
  const [useMultipartUpload, setUseMultipartUpload] = useState<boolean>(true); // Default to multipart
  const [multipartUploadedChunks, setMultipartUploadedChunks] = useState<
    Array<{
      chunkIndex: number;
      partNumber: number;
      etag: string;
      size: number;
      uploadedAt?: string;
      isFinalChunk?: boolean;
    }>
  >([]);
  const [multipartUploadStarted, setMultipartUploadStarted] =
    useState<boolean>(false);

  // Local storage management state
  const [localStorageUsage, setLocalStorageUsage] = useState<{
    totalSize: number;
    fileCount: number;
  }>({ totalSize: 0, fileCount: 0 });
  const [preservedLocalFiles, setPreservedLocalFiles] = useState<
    Array<{
      filePath: string;
      size: number;
      timestamp: string;
      sessionId: string;
    }>
  >([]);

  // Track expected chunks for race condition prevention
  const [expectedChunks, setExpectedChunks] = useState<Set<number>>(new Set());
  const [finalChunkReceived, setFinalChunkReceived] = useState<boolean>(false);

  // Handle new audio chunk events from iOS size-based segmentation
  const handleNewAudioChunk = useCallback(
    async (chunkInfo: {
      filePath: string;
      chunkIndex: number;
      fileSize: number;
      timestamp: number;
      isFinalChunk?: boolean;
    }) => {
      const eventReceiveTime = Date.now();
      console.log('🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====');
      console.log(
        `⏰ Event received at: ${new Date(eventReceiveTime).toISOString()}`,
      );
      console.log('📦 Chunk Info:', JSON.stringify(chunkInfo, null, 2));
      console.log('📦 Current Session ID:', currentSessionId);
      console.log('📦 Is Recording:', isRecording);
      console.log('📦 Use Multipart Upload:', useMultipartUpload);
      console.log(
        `🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk ${chunkInfo.chunkIndex}`,
      );
      console.log(
        `🔍 [FINAL CHUNK] Is final chunk: ${chunkInfo.isFinalChunk || false}`,
      );

      if (chunkInfo.isFinalChunk) {
        console.log(
          `📦 [FINAL CHUNK] Processing final partial chunk (${(
            chunkInfo.fileSize /
            1024 /
            1024
          ).toFixed(2)} MB)`,
        );
        setFinalChunkReceived(true);
      }
      // Alert.alert('New Chunk', `📦 New chunk received! Index: ${chunkInfo.chunkIndex}, Size: ${Math.round(chunkInfo.fileSize / 1024 / 1024 * 100) / 100}MB`);

      if (!currentSessionId) {
        // console.error('❌ No active session for chunk upload');
        return;
      }

      try {
        const uploadStartTime = Date.now();
        const progressKey = `chunk-${chunkInfo.chunkIndex}`;
        setUploadProgress(prev => ({ ...prev, [progressKey]: 0 }));

        console.log(
          `⏱️ [TIMING] Upload started ${
            uploadStartTime - eventReceiveTime
          }ms after event received`,
        );

        if (useMultipartUpload) {
          // Start multipart upload if this is the first chunk
          if (chunkInfo.chunkIndex === 0 && !multipartUploadStarted) {
            console.log(
              '🚀 [IMMEDIATE UPLOAD] Starting session multipart upload for first chunk (index 0)',
            );
            console.log(
              `🔍 [VERIFICATION] This should be the very first chunk of the recording session`,
            );
            const multipartStartTime = Date.now();
            await startSessionMultipartUpload(
              S3_CONFIG.USER_ID,
              currentSessionId,
            );
            console.log(
              `⏱️ [TIMING] Multipart session started in ${
                Date.now() - multipartStartTime
              }ms`,
            );
            setMultipartUploadStarted(true);
          }

          // Upload chunk as part of multipart upload
          console.log(
            `🔍 [IMMEDIATE UPLOAD] Uploading chunk ${
              chunkInfo.chunkIndex
            } as part ${chunkInfo.chunkIndex + 1}`,
          );
          const chunkUploadStartTime = Date.now();

          const uploadResult = await retryUpload(async () => {
            return await uploadSessionChunk(
              chunkInfo.filePath,
              currentSessionId,
              chunkInfo.chunkIndex,
              progress => {
                const percentage = Math.round(
                  (progress.loaded / progress.total) * 100,
                );
                setUploadProgress(prev => ({
                  ...prev,
                  [progressKey]: percentage,
                }));
              },
            );
          });

          console.log(
            `⏱️ [TIMING] Chunk ${chunkInfo.chunkIndex} uploaded in ${
              Date.now() - chunkUploadStartTime
            }ms`,
          );

          // Handle race condition case
          if (uploadResult.raceCondition) {
            console.log(
              `🏁 [RACE CONDITION] Chunk ${chunkInfo.chunkIndex} arrived after multipart completion - skipping`,
            );
            setUploadProgress(prev => {
              const newProgress = { ...prev };
              delete newProgress[progressKey];
              return newProgress;
            });
            return; // Exit early, don't add to uploaded chunks
          }

          // Add to multipart uploaded chunks list with detailed tracking
          setMultipartUploadedChunks(prev => [
            ...prev,
            {
              chunkIndex: chunkInfo.chunkIndex,
              partNumber: chunkInfo.chunkIndex + 1, // Parts are 1-based
              etag: '', // Will be filled by upload response
              size: chunkInfo.fileSize,
              uploadedAt: new Date().toISOString(),
              isFinalChunk: chunkInfo.isFinalChunk || false,
            },
          ]);

          console.log(
            `✅ Successfully uploaded multipart chunk ${
              chunkInfo.chunkIndex
            } (part ${chunkInfo.chunkIndex + 1})`,
          );
        } else {
          // Fallback to single chunk upload
          await retryUpload(async () => {
            return await uploadSingleChunk(
              chunkInfo.filePath,
              S3_CONFIG.USER_ID,
              currentSessionId,
              chunkInfo.chunkIndex,
              chunkInfo.timestamp.toString(),
              progress => {
                const percentage = Math.round(
                  (progress.loaded / progress.total) * 100,
                );
                setUploadProgress(prev => ({
                  ...prev,
                  [progressKey]: percentage,
                }));
              },
            );
          });

          // Add to size-based uploaded chunks list
          setSizeBasedUploadedChunks(prev => [
            ...prev,
            {
              chunkIndex: chunkInfo.chunkIndex,
              s3Key: generateSingleChunkKey(
                S3_CONFIG.USER_ID,
                currentSessionId,
                chunkInfo.chunkIndex,
                chunkInfo.timestamp.toString(),
              ),
              timestamp: chunkInfo.timestamp.toString(),
              size: chunkInfo.fileSize,
              etag: '', // Will be filled by upload response
            },
          ]);

          console.log(
            `✅ Successfully uploaded single chunk ${chunkInfo.chunkIndex}`,
          );
        }

        // Mark as completed
        setUploadProgress(prev => ({ ...prev, [progressKey]: 100 }));

        // 📁 PRESERVE LOCAL FILES: Keep original audio chunks for backup and debugging
        // Local files are now preserved on device for offline access and recovery
        console.log(`� Local file preserved: ${chunkInfo.filePath}`);
        console.log(
          `💾 File size: ${(chunkInfo.fileSize / 1024 / 1024).toFixed(2)} MB`,
        );

        // Track preserved local files
        setPreservedLocalFiles(prev => [
          ...prev,
          {
            filePath: chunkInfo.filePath,
            size: chunkInfo.fileSize,
            timestamp: new Date().toISOString(),
            sessionId: currentSessionId,
          },
        ]);

        // Update local storage usage
        setLocalStorageUsage(prev => ({
          totalSize: prev.totalSize + chunkInfo.fileSize,
          fileCount: prev.fileCount + 1,
        }));
      } catch (error) {
        console.error(
          `❌ Failed to upload chunk ${chunkInfo.chunkIndex}:`,
          error,
        );

        // Track the error
        setUploadErrors(prev => [
          ...prev,
          {
            chunkIndex: chunkInfo.chunkIndex,
            error: error instanceof Error ? error.message : String(error),
            timestamp: new Date().toISOString(),
          },
        ]);

        // Remove from progress tracking on failure
        setUploadProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[`chunk-${chunkInfo.chunkIndex}`];
          return newProgress;
        });
      }
    },
    [currentSessionId, isRecording, useMultipartUpload, multipartUploadStarted],
  );

  useEffect(() => {
    checkPermissionAndSetup();

    // Test AWS credentials on app startup
    testCredentials().then(isValid => {
      if (!isValid) {
        Alert.alert(
          'AWS Configuration Error',
          'AWS credentials are invalid. S3 uploads will not work. Please check the credentials in s3Client.js',
          [{ text: 'OK' }],
        );
      }
    });

    // Calculate initial local storage usage
    calculateLocalStorageUsage();

    // Test the event handler after 5 seconds
    setTimeout(() => {
      console.log('🧪 Testing React Native event handler directly');
      const testData = {
        filePath: '/test/direct/path.m4a',
        chunkIndex: 888,
        fileSize: 5242880,
        timestamp: Date.now(),
      };
      handleNewAudioChunk(testData);
    }, 5000);
  }, []);

  // Set up event listener for size-based audio chunks (iOS only) with proper dependencies
  useEffect(() => {
    if (Platform.OS === 'ios') {
      console.log('📱 Setting up iOS event listener for onNewAudioChunk');
      console.log(
        '📱 Current session ID when setting up listener:',
        currentSessionId,
      );
      console.log(
        '📱 handleNewAudioChunk function:',
        typeof handleNewAudioChunk,
      );

      const { NativeEventEmitter, NativeModules } = require('react-native');
      const eventEmitter = new NativeEventEmitter(
        NativeModules.AudioRecorderModule,
      );

      const chunkSubscription = eventEmitter.addListener(
        'onNewAudioChunk',
        (data: any) => {
          console.log('📱 Raw onNewAudioChunk event received from iOS:', data);
          handleNewAudioChunk(data);
        },
      );

      // Test event listener to verify bridge connection
      const testSubscription = eventEmitter.addListener(
        'onRecordingStarted',
        (data: any) => {
          console.log('🧪 Test event received from iOS:', data);
          Alert.alert(
            'Bridge Test',
            'iOS bridge is working! Recording started event received.',
          );
        },
      );

      console.log('📱 Event listener subscriptions created:', {
        chunkSubscription,
        testSubscription,
      });

      return () => {
        console.log('📱 Removing iOS event listeners');
        chunkSubscription.remove();
        testSubscription.remove();
      };
    }
  }, [handleNewAudioChunk]); // Re-setup listener when handler changes

  // Timer update effect
  useEffect(() => {
    let timerInterval: NodeJS.Timeout;
    let fileCheckInterval: NodeJS.Timeout;

    if (isRecording) {
      timerInterval = setInterval(async () => {
        try {
          const timer = await AudioRecorderModule.getRecordingTimer();
          setRecordingTimer(timer);
        } catch (error) {
          console.error('Failed to get recording timer:', error);
        }
      }, 1000);

      // Check for new files every 10 seconds during recording
      fileCheckInterval = setInterval(async () => {
        try {
          await updateRecordingFiles();
        } catch (error) {
          console.error('Failed to check for new files:', error);
        }
      }, 10000);
    } else {
      setRecordingTimer(null);
    }

    return () => {
      if (timerInterval) {
        clearInterval(timerInterval);
      }
      if (fileCheckInterval) {
        clearInterval(fileCheckInterval);
      }
    };
  }, [isRecording, recordingFiles.length, currentSessionId]);

  // Playback status update effect
  useEffect(() => {
    let playbackInterval: NodeJS.Timeout;

    if (currentlyPlayingFile) {
      playbackInterval = setInterval(async () => {
        try {
          const status = await AudioRecorderModule.getPlaybackStatus();
          setPlaybackStatus(status);

          if (!status.isPlaying && status.currentTime === 0) {
            // Playback finished
            setCurrentlyPlayingFile(null);
            setPlaybackStatus(null);
          }
        } catch (error) {
          console.error('Failed to get playback status:', error);
        }
      }, 100);
    } else {
      setPlaybackStatus(null);
    }

    return () => {
      if (playbackInterval) {
        clearInterval(playbackInterval);
      }
    };
  }, [currentlyPlayingFile]);

  const checkPermissionAndSetup = async () => {
    if (Platform.OS === 'ios') {
      if (!AudioRecorderModule) {
        Alert.alert(
          'Module Error',
          'AudioRecorderModule is not available. Please check the native module setup.',
        );
        return;
      }

      try {
        const result = await AudioRecorderModule.requestMicrophonePermission();
        setPermissionGranted(result.granted);
        if (result.granted) {
          await updateRecordingInfo();
          await updateRecordingFiles();
        }
      } catch (error) {
        console.error('Permission error:', error);
        Alert.alert(
          'Permission Error',
          'Failed to request microphone permission',
        );
      }
    }
  };

  const updateRecordingInfo = async () => {
    try {
      const info = await AudioRecorderModule.getCurrentRecordingInfo();
      setRecordingInfo(info);
      setIsRecording(info.isRecording);
    } catch (error) {
      console.error('Failed to get recording info:', error);
    }
  };

  const updateRecordingFiles = async () => {
    try {
      const result = await AudioRecorderModule.getAllRecordingFiles();
      const newFiles = result.files;

      // For size-based segmentation, chunks are uploaded immediately via events
      // No need to monitor files manually

      setRecordingFiles(newFiles);
    } catch (error) {
      console.error('Failed to get recording files:', error);
    }
  };

  const startRecording = async () => {
    try {
      // Create new session ID for this recording
      const sessionId = Date.now().toString();
      setCurrentSessionId(sessionId);
      setUploadedChunks([]);
      setUploadProgress({});
      setUploadErrors([]);
      setSizeBasedUploadedChunks([]);

      // Reset multipart upload state
      setMultipartUploadedChunks([]);
      setMultipartUploadStarted(false);

      // Reset race condition tracking
      setFinalChunkReceived(false);

      // Always use size-based segmentation now
      console.log('🚀 Starting size-based recording session:', sessionId);
      console.log(
        '📏 Chunks will be uploaded automatically when they reach 5MB',
      );

      console.log(
        `🎬 Starting recording with size-based segmentation (5MB chunks), platform: ${Platform.OS}`,
      );
      console.log(
        '🔍 AudioRecorderModule methods:',
        Object.keys(AudioRecorderModule),
      );
      console.log(
        '🔍 startRecording method type:',
        typeof AudioRecorderModule.startRecording,
      );
      console.log(
        '🔍 CRITICAL DEBUG - Starting recording with size-based segmentation (always enabled)',
      );

      // Call native method with platform-specific parameters
      const result =
        Platform.OS === 'ios'
          ? await AudioRecorderModule.startRecording()
          : await AudioRecorderModule.startRecording(2); // Android still needs segmentMinutes parameter
      console.log('Recording started:', result);
      console.log('S3 Session ID:', sessionId);

      setIsRecording(true);
      await updateRecordingInfo();
      Alert.alert(
        'Success',
        'Recording started successfully! Files will be uploaded to S3.',
      );
    } catch (error) {
      console.error('Start recording error:', error);
      Alert.alert(
        'Error',
        `Failed to start recording: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      );
    }
  };

  const stopRecording = async () => {
    try {
      const result = await AudioRecorderModule.stopRecording();
      console.log('Recording stopped:', result);
      setIsRecording(false);

      // Wait for any pending uploads to complete before processing completion
      console.log(
        `⏳ [RACE CONDITION FIX] Waiting for pending uploads to complete...`,
      );
      console.log(
        `🔍 [RACE CONDITION FIX] Final chunk received: ${finalChunkReceived}`,
      );

      // Wait a short time to allow final chunk events to be processed
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Wait for final chunk to be processed if it was received
      if (finalChunkReceived) {
        console.log(
          `⏳ [RACE CONDITION FIX] Final chunk was received, waiting for it to complete upload...`,
        );

        let waitTime = 0;
        const maxWaitTime = 15000; // 15 seconds max wait
        while (
          Object.keys(uploadProgress).length > 0 &&
          waitTime < maxWaitTime
        ) {
          await new Promise(resolve => setTimeout(resolve, 500));
          waitTime += 500;
          console.log(
            `⏳ [RACE CONDITION FIX] Still waiting for final chunk... ${
              Object.keys(uploadProgress).length
            } uploads remaining`,
          );
        }

        if (Object.keys(uploadProgress).length > 0) {
          console.warn(
            `⚠️ [RACE CONDITION FIX] Timeout waiting for final chunk upload, proceeding anyway`,
          );
        }
      }

      // Check for any remaining active uploads
      const activeUploads = Object.keys(uploadProgress).length;
      if (activeUploads > 0) {
        console.log(
          `⏳ [RACE CONDITION FIX] Waiting for ${activeUploads} remaining uploads to finish...`,
        );

        // Wait for uploads to complete (max 10 more seconds)
        let waitTime = 0;
        const maxWaitTime = 10000;
        while (
          Object.keys(uploadProgress).length > 0 &&
          waitTime < maxWaitTime
        ) {
          await new Promise(resolve => setTimeout(resolve, 1000));
          waitTime += 1000;
          console.log(
            `⏳ [RACE CONDITION FIX] Still waiting... ${
              Object.keys(uploadProgress).length
            } uploads remaining`,
          );
        }
      }

      // Handle upload completion based on upload method
      if (currentSessionId) {
        console.log(
          `\n🔍 [DEBUG] Processing upload completion for session: ${currentSessionId}`,
        );
        console.log(`🔍 [DEBUG] useMultipartUpload: ${useMultipartUpload}`);
        console.log(
          `🔍 [DEBUG] multipartUploadStarted: ${multipartUploadStarted}`,
        );
        console.log(
          `🔍 [DEBUG] multipartUploadedChunks.length: ${multipartUploadedChunks.length}`,
        );
        console.log(
          `🔍 [DEBUG] sizeBasedUploadedChunks.length: ${sizeBasedUploadedChunks.length}`,
        );

        if (
          useMultipartUpload &&
          multipartUploadStarted &&
          multipartUploadedChunks.length > 0
        ) {
          // Complete session-based multipart upload
          try {
            console.log('🏁 Completing session multipart upload...');
            console.log(
              `📦 Total multipart chunks to combine: ${multipartUploadedChunks.length}`,
            );
            console.log(
              `🔍 [DEBUG] Multipart chunks details:`,
              multipartUploadedChunks,
            );

            const multipartResult = (await finishSessionMultipartUpload(
              currentSessionId,
            )) as any;
            console.log(
              '✅ Session multipart upload completed:',
              multipartResult.location || 'Success',
            );
            console.log(
              `📁 Final consolidated file: ${multipartResult.finalKey}`,
            );
            console.log(
              `📊 Total parts combined: ${multipartResult.totalParts}`,
            );
            console.log(
              `🔍 [DEBUG] Complete multipart result:`,
              multipartResult,
            );

            // Upload manifest for the consolidated file
            console.log(`🔍 [DEBUG] Uploading multipart manifest...`);
            const manifestResult = await uploadMultipartManifest(
              S3_CONFIG.USER_ID,
              currentSessionId,
              multipartResult,
            );
            console.log('📋 Multipart manifest uploaded successfully');
            console.log(`🔍 [DEBUG] Manifest result:`, manifestResult);

            // Clean up session data after successful completion
            cleanupSessionUpload(currentSessionId);

            Alert.alert(
              'Success',
              `Recording completed! ${multipartResult.totalParts} chunks consolidated into single file: ${multipartResult.finalKey}`,
            );
          } catch (error) {
            console.error(
              '❌ Failed to complete session multipart upload:',
              error,
            );
            console.error(`🔍 [DEBUG] Multipart completion error:`, {
              name: (error as any)?.name,
              message: (error as any)?.message,
              stack: (error as any)?.stack,
            });

            setUploadErrors(prev => [
              ...prev,
              {
                chunkIndex: -1,
                error: `Failed to complete multipart upload: ${
                  error instanceof Error ? error.message : 'Unknown error'
                }`,
                timestamp: new Date().toISOString(),
              },
            ]);

            // Try to abort the multipart upload on failure
            try {
              console.log(`🔍 [DEBUG] Attempting to abort multipart upload...`);
              await abortSessionMultipartUpload(currentSessionId);
              console.log('🛑 Aborted failed multipart upload');
            } catch (abortError) {
              console.error('❌ Failed to abort multipart upload:', abortError);
            }
          }
        } else if (sizeBasedUploadedChunks.length > 0) {
          // Fallback: size-based uploads with individual chunks
          try {
            console.log(
              '📋 Uploading size-based manifest for individual chunks...',
            );
            await uploadSizeBasedManifest(
              S3_CONFIG.USER_ID,
              currentSessionId,
              sizeBasedUploadedChunks,
            );
            console.log('✅ Size-based manifest uploaded successfully');
          } catch (error) {
            console.error('❌ Failed to upload size-based manifest:', error);
            setUploadErrors(prev => [
              ...prev,
              {
                chunkIndex: -1,
                error: `Failed to upload manifest: ${
                  error instanceof Error ? error.message : 'Unknown error'
                }`,
                timestamp: new Date().toISOString(),
              },
            ]);
          }
        } else if (uploadedChunks.length > 0) {
          // Legacy: Complete old-style multipart upload
          try {
            console.log('🏁 Completing legacy multipart upload...');
            const result = await finishMultipartUpload();
            console.log(
              '✅ Legacy multipart upload completed:',
              (result as any).Location || 'Success',
            );

            // Also upload manifest for tracking
            await uploadManifest(
              S3_CONFIG.USER_ID,
              currentSessionId,
              uploadedChunks,
            );
            console.log('📋 Legacy manifest uploaded successfully');
          } catch (error) {
            console.error(
              '❌ Failed to complete legacy multipart upload:',
              error,
            );
            setUploadErrors(prev => [
              ...prev,
              {
                chunkIndex: -1,
                error: `Failed to complete upload: ${
                  error instanceof Error ? error.message : 'Unknown error'
                }`,
                timestamp: new Date().toISOString(),
              },
            ]);
          }
        }
      }

      await updateRecordingInfo();
      await updateRecordingFiles();

      // Show appropriate success message based on upload mode
      if (!useMultipartUpload && sizeBasedUploadedChunks.length > 0) {
        Alert.alert(
          'Success',
          `Recording stopped successfully! ${sizeBasedUploadedChunks.length} individual chunks uploaded to S3.`,
        );
      } else if (uploadedChunks.length > 0) {
        Alert.alert(
          'Success',
          `Recording stopped successfully! ${uploadedChunks.length} chunks uploaded to S3.`,
        );
      } else {
        Alert.alert('Success', 'Recording stopped successfully!');
      }
    } catch (error) {
      console.error('Stop recording error:', error);
      Alert.alert(
        'Error',
        `Failed to stop recording: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      );
    }
  };

  const refreshFiles = async () => {
    await updateRecordingFiles();
    await updateRecordingInfo();
  };

  // Local storage management functions
  const calculateLocalStorageUsage = async () => {
    try {
      const RNFS = require('react-native-fs');
      let totalSize = 0;
      let fileCount = 0;

      for (const file of preservedLocalFiles) {
        try {
          const stats = await RNFS.stat(file.filePath);
          totalSize += stats.size;
          fileCount++;
        } catch (error) {
          console.warn(`⚠️ File no longer exists: ${file.filePath}`);
        }
      }

      setLocalStorageUsage({ totalSize, fileCount });
      console.log(
        `📊 Local storage usage: ${(totalSize / 1024 / 1024).toFixed(
          2,
        )} MB (${fileCount} files)`,
      );
      return { totalSize, fileCount };
    } catch (error) {
      console.error('❌ Failed to calculate local storage usage:', error);
      return { totalSize: 0, fileCount: 0 };
    }
  };

  const clearLocalFiles = async (sessionId?: string) => {
    try {
      const RNFS = require('react-native-fs');
      const filesToDelete = sessionId
        ? preservedLocalFiles.filter(file => file.sessionId === sessionId)
        : preservedLocalFiles;

      let deletedCount = 0;
      let freedSpace = 0;

      for (const file of filesToDelete) {
        try {
          await RNFS.unlink(file.filePath);
          deletedCount++;
          freedSpace += file.size;
          console.log(`🗑️ Deleted local file: ${file.filePath}`);
        } catch (error) {
          console.warn(`⚠️ Failed to delete file: ${file.filePath}`, error);
        }
      }

      // Update preserved files list
      setPreservedLocalFiles(prev =>
        sessionId ? prev.filter(file => file.sessionId !== sessionId) : [],
      );

      // Recalculate storage usage
      await calculateLocalStorageUsage();

      Alert.alert(
        'Local Files Cleared',
        `Deleted ${deletedCount} files, freed ${(
          freedSpace /
          1024 /
          1024
        ).toFixed(2)} MB`,
      );

      return { deletedCount, freedSpace };
    } catch (error) {
      console.error('❌ Failed to clear local files:', error);
      Alert.alert('Error', 'Failed to clear local files');
      return { deletedCount: 0, freedSpace: 0 };
    }
  };

  const clearAllFiles = async () => {
    Alert.alert(
      'Clear All Recordings',
      'Are you sure you want to delete all recordings? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete All',
          style: 'destructive',
          onPress: async () => {
            try {
              // Stop any current playback
              if (currentlyPlayingFile) {
                await AudioRecorderModule.stopPlayback();
                setCurrentlyPlayingFile(null);
                setPlaybackStatus(null);
              }

              // Clear files from native storage
              try {
                // Check if the native method is available
                if (AudioRecorderModule.clearAllRecordingFiles) {
                  const result =
                    await AudioRecorderModule.clearAllRecordingFiles();

                  if (result.success) {
                    // Refresh to get updated state from native side
                    await refreshFiles();
                    Alert.alert('Success', result.message);
                  } else {
                    Alert.alert('Partial Success', result.message);
                    // Still refresh to show current state
                    await refreshFiles();
                  }
                } else {
                  // Fallback: Clear UI state and show message
                  setRecordingFiles([]);
                  Alert.alert(
                    'Info',
                    'Files cleared from display. Please rebuild the app to enable native file deletion.',
                  );
                }
              } catch (methodError) {
                // Fallback for when method doesn't exist
                setRecordingFiles([]);
                Alert.alert(
                  'Info',
                  'Files cleared from display. Please rebuild the app to enable native file deletion.',
                );
              }
            } catch (error) {
              console.error('Clear files error:', error);
              Alert.alert(
                'Error',
                `Failed to clear files: ${
                  error instanceof Error ? error.message : 'Unknown error'
                }`,
              );
            }
          },
        },
      ],
    );
  };

  // Sort files by creation date (most recent first)
  const sortedRecordingFiles = [...recordingFiles].sort((a, b) => {
    const dateA = new Date(a.creationDate).getTime();
    const dateB = new Date(b.creationDate).getTime();
    return dateB - dateA; // Descending order (newest first)
  });

  // S3 Upload Functions - size-based segmentation only

  const playRecording = async (filePath: string) => {
    try {
      // Stop any current playback
      if (currentlyPlayingFile) {
        await AudioRecorderModule.stopPlayback();
      }

      const result = await AudioRecorderModule.playRecording(filePath);
      console.log('Playback started:', result);
      setCurrentlyPlayingFile(filePath);
    } catch (error) {
      console.error('Play recording error:', error);
      Alert.alert(
        'Error',
        `Failed to play recording: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      );
    }
  };

  const stopPlayback = async () => {
    try {
      await AudioRecorderModule.stopPlayback();
      setCurrentlyPlayingFile(null);
      setPlaybackStatus(null);
    } catch (error) {
      console.error('Stop playback error:', error);
      Alert.alert(
        'Error',
        `Failed to stop playback: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      );
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (!permissionGranted) {
    return (
      <View style={[styles.container, styles.centered]}>
        <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
        <Text style={[styles.title, { color: isDarkMode ? '#fff' : '#000' }]}>
          Microphone Permission Required
        </Text>
        <Text
          style={[styles.subtitle, { color: isDarkMode ? '#ccc' : '#666' }]}
        >
          Please grant microphone permission to use this app.
        </Text>
        <TouchableOpacity
          style={styles.button}
          onPress={checkPermissionAndSetup}
        >
          <Text style={styles.buttonText}>Request Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // App Bar Component
  const AppBar = () => (
    <View
      style={[
        styles.appBar,
        { backgroundColor: isDarkMode ? '#1a1a1a' : '#f8f9fa' },
      ]}
    >
      <View style={styles.appBarContent}>
        <Text
          style={[styles.appBarTitle, { color: isDarkMode ? '#fff' : '#000' }]}
        >
          Recorder
        </Text>
        <TouchableOpacity
          style={[
            styles.clearButton,
            { backgroundColor: isDarkMode ? '#dc3545' : '#e74c3c' },
          ]}
          onPress={clearAllFiles}
        >
          <Text style={styles.clearButtonText}>🗑️ Clear All</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#000' : '#fff' },
      ]}
    >
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />

      <AppBar />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Recording Status */}
        <View style={styles.statusContainer}>
          <Text
            style={[styles.statusText, { color: isDarkMode ? '#fff' : '#000' }]}
          >
            Status: {isRecording ? '🔴 Recording' : '⏹️ Stopped'}
          </Text>

          {/* Recording Timer */}
          {recordingTimer && (
            <View style={styles.timerContainer}>
              <Text
                style={[
                  styles.timerText,
                  { color: isDarkMode ? '#ff6b6b' : '#e74c3c' },
                ]}
              >
                Total: {recordingTimer.formattedTotal}
              </Text>
              <Text
                style={[
                  styles.timerText,
                  { color: isDarkMode ? '#4ecdc4' : '#3498db' },
                ]}
              >
                Segment: {recordingTimer.formattedSegment}
              </Text>
            </View>
          )}

          {recordingInfo && (
            <>
              <Text
                style={[
                  styles.infoText,
                  { color: isDarkMode ? '#ccc' : '#666' },
                ]}
              >
                Current File:{' '}
                {recordingInfo.currentFilePath
                  ? recordingInfo.currentFilePath.split('/').pop()
                  : 'None'}
              </Text>
              <Text
                style={[
                  styles.infoText,
                  { color: isDarkMode ? '#ccc' : '#666' },
                ]}
              >
                Segment: {recordingInfo.recordingCounter} | Duration:{' '}
                {recordingInfo.segmentDurationMinutes}min
              </Text>

              {/* S3 Upload Status */}
              {currentSessionId && (
                <>
                  <Text
                    style={[
                      styles.infoText,
                      { color: isDarkMode ? '#4ecdc4' : '#3498db' },
                    ]}
                  >
                    S3 Session: {currentSessionId}
                  </Text>
                  <Text
                    style={[
                      styles.infoText,
                      { color: isDarkMode ? '#9b59b6' : '#8e44ad' },
                    ]}
                  >
                    Upload Mode:{' '}
                    {useMultipartUpload
                      ? 'Multipart (Consolidated)'
                      : 'Individual Chunks'}
                  </Text>
                  {useMultipartUpload ? (
                    <>
                      <Text
                        style={[
                          styles.infoText,
                          { color: isDarkMode ? '#2ecc71' : '#27ae60' },
                        ]}
                      >
                        Multipart Chunks: {multipartUploadedChunks.length}
                      </Text>
                      {multipartUploadStarted && (
                        <Text
                          style={[
                            styles.infoText,
                            { color: isDarkMode ? '#e67e22' : '#d35400' },
                          ]}
                        >
                          ⚡ Multipart Upload Active
                        </Text>
                      )}
                      {multipartUploadedChunks.length > 0 && (
                        <>
                          <Text
                            style={[
                              styles.infoText,
                              { color: isDarkMode ? '#3498db' : '#2980b9' },
                            ]}
                          >
                            Total Size:{' '}
                            {(
                              multipartUploadedChunks.reduce(
                                (sum, chunk) => sum + chunk.size,
                                0,
                              ) /
                              1024 /
                              1024
                            ).toFixed(2)}{' '}
                            MB
                          </Text>
                          <Text
                            style={[
                              styles.infoText,
                              { color: isDarkMode ? '#9b59b6' : '#8e44ad' },
                            ]}
                          >
                            Parts:{' '}
                            {multipartUploadedChunks
                              .map(c => c.partNumber)
                              .join(', ')}
                            {multipartUploadedChunks.some(
                              c => c.isFinalChunk,
                            ) && ' (+ final)'}
                          </Text>
                        </>
                      )}
                    </>
                  ) : (
                    <>
                      <Text
                        style={[
                          styles.infoText,
                          { color: isDarkMode ? '#2ecc71' : '#27ae60' },
                        ]}
                      >
                        Size-Based Chunks: {sizeBasedUploadedChunks.length}
                      </Text>
                      {sizeBasedUploadedChunks.length > 0 && (
                        <Text
                          style={[
                            styles.infoText,
                            { color: isDarkMode ? '#3498db' : '#2980b9' },
                          ]}
                        >
                          Total Size:{' '}
                          {(
                            sizeBasedUploadedChunks.reduce(
                              (sum, chunk) => sum + chunk.size,
                              0,
                            ) /
                            1024 /
                            1024
                          ).toFixed(2)}{' '}
                          MB
                        </Text>
                      )}
                    </>
                  )}
                  {Object.keys(uploadProgress).length > 0 && (
                    <Text
                      style={[
                        styles.infoText,
                        { color: isDarkMode ? '#f39c12' : '#e67e22' },
                      ]}
                    >
                      Uploading: {Object.keys(uploadProgress).length} files
                    </Text>
                  )}

                  {/* Local Storage Status */}
                  <Text
                    style={[
                      styles.infoText,
                      { color: isDarkMode ? '#95a5a6' : '#7f8c8d' },
                    ]}
                  >
                    Local Storage:{' '}
                    {(localStorageUsage.totalSize / 1024 / 1024).toFixed(2)} MB
                    ({localStorageUsage.fileCount} files)
                  </Text>
                  {preservedLocalFiles.length > 0 && (
                    <Text
                      style={[
                        styles.infoText,
                        { color: isDarkMode ? '#f39c12' : '#e67e22' },
                      ]}
                    >
                      📁 {preservedLocalFiles.length} local chunks preserved
                    </Text>
                  )}
                  {uploadErrors.length > 0 && (
                    <Text
                      style={[
                        styles.infoText,
                        { color: isDarkMode ? '#e74c3c' : '#c0392b' },
                      ]}
                    >
                      Upload Errors: {uploadErrors.length}
                    </Text>
                  )}
                </>
              )}
            </>
          )}
        </View>

        {/* Control Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, isRecording && styles.buttonDisabled]}
            onPress={startRecording}
            disabled={isRecording}
          >
            <Text style={styles.buttonText}>Start Recording</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.button,
              styles.stopButton,
              !isRecording && styles.buttonDisabled,
            ]}
            onPress={stopRecording}
            disabled={!isRecording}
          >
            <Text style={styles.buttonText}>Stop Recording</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.refreshButton]}
            onPress={refreshFiles}
          >
            <Text style={styles.buttonText}>Refresh Files</Text>
          </TouchableOpacity>
        </View>

        {/* Recording Files List */}
        <View style={styles.filesContainer}>
          <Text
            style={[
              styles.sectionTitle,
              { color: isDarkMode ? '#fff' : '#000' },
            ]}
          >
            Recording Files ({recordingFiles.length})
          </Text>

          {sortedRecordingFiles.length === 0 ? (
            <Text
              style={[
                styles.noFilesText,
                { color: isDarkMode ? '#ccc' : '#666' },
              ]}
            >
              No recording files found
            </Text>
          ) : (
            sortedRecordingFiles.map((file, index) => (
              <View
                key={index}
                style={[
                  styles.fileItem,
                  { borderColor: isDarkMode ? '#333' : '#ddd' },
                ]}
              >
                <Text
                  style={[
                    styles.fileName,
                    { color: isDarkMode ? '#fff' : '#000' },
                  ]}
                >
                  {file.fileName}
                </Text>
                <Text
                  style={[
                    styles.fileInfo,
                    { color: isDarkMode ? '#ccc' : '#666' },
                  ]}
                >
                  Size: {formatFileSize(file.fileSize)}
                </Text>
                <Text
                  style={[
                    styles.fileInfo,
                    { color: isDarkMode ? '#ccc' : '#666' },
                  ]}
                >
                  Created: {formatDate(file.creationDate)}
                </Text>

                {/* Playback Controls */}
                <View style={styles.playbackControls}>
                  {currentlyPlayingFile === file.filePath ? (
                    <>
                      {playbackStatus && (
                        <View style={styles.playbackInfo}>
                          <Text
                            style={[
                              styles.playbackTime,
                              { color: isDarkMode ? '#4ecdc4' : '#3498db' },
                            ]}
                          >
                            {playbackStatus.formattedCurrentTime} /{' '}
                            {playbackStatus.formattedDuration}
                          </Text>
                          <View
                            style={[
                              styles.progressBar,
                              { backgroundColor: isDarkMode ? '#333' : '#ddd' },
                            ]}
                          >
                            <View
                              style={[
                                styles.progressFill,
                                {
                                  width: `${playbackStatus.progress * 100}%`,
                                  backgroundColor: isDarkMode
                                    ? '#4ecdc4'
                                    : '#3498db',
                                },
                              ]}
                            />
                          </View>
                        </View>
                      )}
                      <TouchableOpacity
                        style={[styles.playButton, styles.stopButton]}
                        onPress={stopPlayback}
                      >
                        <Text style={styles.playButtonText}>⏹️ Stop</Text>
                      </TouchableOpacity>
                    </>
                  ) : (
                    <TouchableOpacity
                      style={styles.playButton}
                      onPress={() => playRecording(file.filePath)}
                    >
                      <Text style={styles.playButtonText}>▶️ Play</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  statusContainer: {
    backgroundColor: 'rgba(0,0,0,0.05)',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  statusText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 2,
  },
  buttonContainer: {
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  stopButton: {
    backgroundColor: '#FF3B30',
  },
  refreshButton: {
    backgroundColor: '#34C759',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
    opacity: 0.6,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  filesContainer: {
    marginTop: 10,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  noFilesText: {
    fontSize: 16,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  fileItem: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
  },
  fileName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  fileInfo: {
    fontSize: 14,
    marginBottom: 2,
  },
  timerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.1)',
  },
  timerText: {
    fontSize: 16,
    fontWeight: 'bold',
    fontFamily: 'Courier',
  },
  playbackControls: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  playbackInfo: {
    marginBottom: 10,
  },
  playbackTime: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 5,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  playButton: {
    backgroundColor: '#27ae60',
    padding: 8,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 5,
  },
  playButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  appBar: {
    paddingTop: Platform.OS === 'ios' ? 50 : 20, // Account for status bar
    paddingBottom: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  appBarContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  appBarTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginLeft: 10,
  },
  clearButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  toggleButton: {
    padding: 15,
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 8,
    borderWidth: 2,
    alignItems: 'center',
  },
  toggleActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#45a049',
  },
  toggleInactive: {
    backgroundColor: '#f0f0f0',
    borderColor: '#ccc',
  },
  toggleText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  toggleSubtext: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
  },
});

export default App;
