#!/bin/bash

# Test script for iOS AlwaysOnRecorder build
echo "🚀 Starting iOS build test for AlwaysOnRecorder..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if iOS directory exists
if [ ! -d "ios" ]; then
    echo "❌ Error: ios directory not found."
    exit 1
fi

echo "📦 Installing npm dependencies..."
npm install

echo "🍎 Installing iOS dependencies..."
cd ios
pod install
cd ..

echo "🔨 Building iOS project..."
npx react-native run-ios --simulator="iPhone 15"

echo "✅ Build completed! Check the simulator for the app."
echo ""
echo "📋 Enhanced Testing Instructions:"
echo ""
echo "🎤 Recording Features:"
echo "1. Grant microphone permission when prompted"
echo "2. Tap 'Start Recording' to begin continuous recording"
echo "3. Observe real-time timer showing total and segment duration"
echo "4. Test background recording by pressing home button"
echo "5. Check iOS notification showing recording progress"
echo "6. Test screen lock recording by locking the device"
echo "7. Return to app and tap 'Stop Recording'"
echo ""
echo "🔊 Playback Features:"
echo "8. Check 'Recording Files' section for generated segments"
echo "9. Tap '▶️ Play' button on any recorded file"
echo "10. Observe playback progress bar and timer"
echo "11. Test '⏹️ Stop' button during playback"
echo "12. Try playing different files (should stop current playback)"
echo ""
echo "🔍 Expected Enhanced Behavior:"
echo "- Real-time timer updates every second during recording"
echo "- Persistent iOS notification during background recording"
echo "- Notification updates every 5 seconds with current duration"
echo "- Smooth playback with progress indication"
echo "- Audio session management between recording and playback"
echo "- Timer resets properly when starting new recording sessions"
echo "- Notification disappears when recording stops"
echo "- Playback controls work independently of recording state"
