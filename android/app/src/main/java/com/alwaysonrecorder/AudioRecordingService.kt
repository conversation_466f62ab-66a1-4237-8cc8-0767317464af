package com.alwaysonrecorder

import android.app.*
import android.content.Context
import android.content.Intent
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.MediaRecorder
import android.os.*
import android.util.Log
import androidx.core.app.NotificationCompat
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

class AudioRecordingService : Service() {
    
    companion object {
        private const val TAG = "AudioRecordingService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "AudioRecordingChannel"
        
        // Actions
        const val ACTION_START_RECORDING = "START_RECORDING"
        const val ACTION_STOP_RECORDING = "STOP_RECORDING"
        
        // Extras
        const val EXTRA_SEGMENT_MINUTES = "segment_minutes"
    }
    
    private var mediaRecorder: MediaRecorder? = null
    private var isRecording = false
    private var recordingStartTime: Long = 0
    private var currentSegmentStartTime: Long = 0
    private var recordingCounter = 0
    private var segmentDurationMinutes = 5
    private var currentRecordingFile: File? = null
    
    // Handlers and timers
    private val handler = Handler(Looper.getMainLooper())
    private var segmentRunnable: Runnable? = null
    private var notificationUpdateRunnable: Runnable? = null
    
    // Audio focus
    private lateinit var audioManager: AudioManager
    private var audioFocusRequest: Any? = null
    private var playbackAudioFocusRequest: Any? = null

    // Wake lock for background recording
    private var wakeLock: PowerManager.WakeLock? = null

    // Playback properties
    private var mediaPlayer: MediaPlayer? = null
    private var isPlaying = false
    private var currentPlayingFile: String? = null
    private var playbackStartTime: Long = 0
    
    // Binder for service communication
    private val binder = AudioRecordingBinder()
    
    inner class AudioRecordingBinder : Binder() {
        fun getService(): AudioRecordingService = this@AudioRecordingService
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "AudioRecordingService created")

        audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        createNotificationChannel()
        acquireWakeLock()
    }
    
    override fun onBind(intent: Intent?): IBinder {
        return binder
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_RECORDING -> {
                val segmentMinutes = intent.getIntExtra(EXTRA_SEGMENT_MINUTES, 5)
                startRecording(segmentMinutes)
            }
            ACTION_STOP_RECORDING -> {
                stopRecording()
            }
        }
        return START_STICKY
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Audio Recording",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Ongoing audio recording notification"
                setSound(null, null)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(recordingDuration: String = "00:00:00"): Notification {
        val stopIntent = Intent(this, AudioRecordingService::class.java).apply {
            action = ACTION_STOP_RECORDING
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Intent to open the app when notification is tapped
        val openAppIntent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val openAppPendingIntent = PendingIntent.getActivity(
            this, 0, openAppIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Audio Recording in Progress")
            .setContentText("Recording time: $recordingDuration")
            .setSmallIcon(android.R.drawable.ic_btn_speak_now)
            .setContentIntent(openAppPendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .addAction(
                android.R.drawable.ic_media_pause,
                "Stop Recording",
                stopPendingIntent
            )
            .build()
    }

    private fun acquireWakeLock() {
        try {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "AudioRecorder::RecordingWakeLock"
            ).apply {
                acquire(10 * 60 * 1000L /*10 minutes*/)
            }
            Log.d(TAG, "Wake lock acquired")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to acquire wake lock", e)
        }
    }

    private fun releaseWakeLock() {
        try {
            wakeLock?.let {
                if (it.isHeld) {
                    it.release()
                    Log.d(TAG, "Wake lock released")
                }
            }
            wakeLock = null
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing wake lock", e)
        }
    }
    
    fun startRecording(segmentMinutes: Int): Boolean {
        if (isRecording) {
            Log.w(TAG, "Recording already in progress")
            return false
        }
        
        segmentDurationMinutes = segmentMinutes
        recordingCounter = 1
        recordingStartTime = System.currentTimeMillis()
        currentSegmentStartTime = recordingStartTime
        
        return if (requestAudioFocus()) {
            try {
                startNewRecordingSegment()
                isRecording = true
                startForeground(NOTIFICATION_ID, createNotification())
                setupSegmentTimer()
                setupNotificationUpdateTimer()
                Log.d(TAG, "Recording started successfully")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start recording", e)
                releaseAudioFocus()
                false
            }
        } else {
            Log.e(TAG, "Failed to request audio focus")
            false
        }
    }
    
    fun stopRecording(): Boolean {
        if (!isRecording) {
            Log.w(TAG, "No recording in progress")
            return false
        }
        
        try {
            stopCurrentRecording()
            isRecording = false
            releaseAudioFocus()
            stopForeground(true)
            stopSelf()
            Log.d(TAG, "Recording stopped successfully")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping recording", e)
            return false
        }
    }
    
    private fun startNewRecordingSegment() {
        try {
            // Stop current recording if exists
            mediaRecorder?.apply {
                try {
                    stop()
                } catch (e: RuntimeException) {
                    Log.w(TAG, "Error stopping previous MediaRecorder", e)
                }
                release()
            }

            // Create new recording file
            currentRecordingFile = generateRecordingFile(recordingCounter)

            // Ensure the parent directory exists
            currentRecordingFile?.parentFile?.let { parentDir ->
                if (!parentDir.exists()) {
                    parentDir.mkdirs()
                }
            }

            // Setup MediaRecorder with high-quality settings matching iOS
            mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                MediaRecorder(this)
            } else {
                @Suppress("DEPRECATION")
                MediaRecorder()
            }.apply {
                // Audio source - use MIC for general recording
                setAudioSource(MediaRecorder.AudioSource.MIC)

                // Output format - MPEG_4 for .m4a files
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)

                // Audio encoder - AAC for high quality
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)

                // Audio settings to match iOS (44.1kHz, mono, high quality)
                setAudioSamplingRate(44100)
                setAudioChannels(1)
                setAudioEncodingBitRate(128000) // 128 kbps for good quality

                // Set output file
                setOutputFile(currentRecordingFile?.absolutePath)

                // Prepare and start
                prepare()
                start()
            }

            currentSegmentStartTime = System.currentTimeMillis()
            Log.d(TAG, "Started recording segment $recordingCounter to: ${currentRecordingFile?.absolutePath}")

        } catch (e: IOException) {
            Log.e(TAG, "Failed to start new recording segment", e)
            throw e
        } catch (e: IllegalStateException) {
            Log.e(TAG, "MediaRecorder in illegal state", e)
            throw e
        } catch (e: RuntimeException) {
            Log.e(TAG, "Runtime error starting recording", e)
            throw e
        }
    }
    
    private fun generateRecordingFile(counter: Int): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
            .format(Date(recordingStartTime))
        val filename = "recording_${timestamp}_${String.format("%03d", counter)}.m4a"

        val recordingsDir = File(getExternalFilesDir(null), "recordings")
        if (!recordingsDir.exists()) {
            val created = recordingsDir.mkdirs()
            Log.d(TAG, "Created recordings directory: $created")
        }

        val file = File(recordingsDir, filename)
        Log.d(TAG, "Generated recording file path: ${file.absolutePath}")
        return file
    }

    fun getRecordingsDirectory(): File {
        return File(getExternalFilesDir(null), "recordings")
    }

    fun getAllRecordingFiles(): List<File> {
        val recordingsDir = getRecordingsDirectory()
        return if (recordingsDir.exists()) {
            recordingsDir.listFiles { file ->
                file.isFile && file.name.endsWith(".m4a") && file.name.startsWith("recording_")
            }?.sortedBy { it.lastModified() } ?: emptyList()
        } else {
            emptyList()
        }
    }

    fun deleteRecordingFile(filePath: String): Boolean {
        return try {
            val file = File(filePath)
            val deleted = file.delete()
            Log.d(TAG, "Deleted recording file $filePath: $deleted")
            deleted
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting recording file $filePath", e)
            false
        }
    }

    fun getRecordingFileSize(filePath: String): Long {
        return try {
            File(filePath).length()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting file size for $filePath", e)
            0L
        }
    }

    // Playback methods
    fun startPlayback(filePath: String): Boolean {
        return try {
            // Stop any current playback
            stopPlayback()

            val file = File(filePath)
            if (!file.exists()) {
                Log.e(TAG, "Playback file does not exist: $filePath")
                return false
            }

            // Request audio focus for playback
            if (!requestPlaybackAudioFocus()) {
                Log.e(TAG, "Failed to get audio focus for playback")
                return false
            }

            // Ensure volume is not muted
            ensureVolumeForPlayback()

            mediaPlayer = MediaPlayer().apply {
                // Set audio attributes for proper playback
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    setAudioAttributes(
                        android.media.AudioAttributes.Builder()
                            .setUsage(android.media.AudioAttributes.USAGE_MEDIA)
                            .setContentType(android.media.AudioAttributes.CONTENT_TYPE_MUSIC)
                            .build()
                    )
                } else {
                    @Suppress("DEPRECATION")
                    setAudioStreamType(AudioManager.STREAM_MUSIC)
                }

                // Set volume to maximum
                setVolume(1.0f, 1.0f)

                setDataSource(filePath)
                setOnCompletionListener {
                    Log.d(TAG, "Playback completed")
                    stopPlayback()
                }
                setOnErrorListener { _, what, extra ->
                    Log.e(TAG, "MediaPlayer error: what=$what, extra=$extra")
                    stopPlayback()
                    true
                }
                setOnPreparedListener {
                    Log.d(TAG, "MediaPlayer prepared, starting playback")
                    start()
                }

                prepareAsync() // Use async prepare for better performance
            }

            isPlaying = true
            currentPlayingFile = filePath
            playbackStartTime = System.currentTimeMillis()

            Log.d(TAG, "Started playback preparation for: $filePath")
            true

        } catch (e: Exception) {
            Log.e(TAG, "Error starting playback", e)
            stopPlayback()
            false
        }
    }

    fun stopPlayback() {
        try {
            mediaPlayer?.apply {
                if (isPlaying) {
                    stop()
                }
                release()
            }
            mediaPlayer = null
            isPlaying = false
            currentPlayingFile = null
            playbackStartTime = 0

            // Release audio focus for playback
            releasePlaybackAudioFocus()

            Log.d(TAG, "Playback stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping playback", e)
        }
    }

    fun getPlaybackStatus(): Map<String, Any> {
        return try {
            if (isPlaying && mediaPlayer != null) {
                val currentPosition = mediaPlayer?.currentPosition ?: 0
                val duration = mediaPlayer?.duration ?: 0
                val progress = if (duration > 0) currentPosition.toFloat() / duration.toFloat() else 0f

                mapOf(
                    "isPlaying" to true,
                    "currentTime" to currentPosition,
                    "duration" to duration,
                    "progress" to progress,
                    "filePath" to (currentPlayingFile ?: ""),
                    "formattedCurrentTime" to formatDuration(currentPosition.toLong()),
                    "formattedDuration" to formatDuration(duration.toLong())
                )
            } else {
                mapOf(
                    "isPlaying" to false,
                    "currentTime" to 0,
                    "duration" to 0,
                    "progress" to 0f,
                    "filePath" to "",
                    "formattedCurrentTime" to "00:00:00",
                    "formattedDuration" to "00:00:00"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting playback status", e)
            mapOf(
                "isPlaying" to false,
                "currentTime" to 0,
                "duration" to 0,
                "progress" to 0f,
                "filePath" to "",
                "formattedCurrentTime" to "00:00:00",
                "formattedDuration" to "00:00:00"
            )
        }
    }
    
    private fun stopCurrentRecording() {
        // Cancel timers
        segmentRunnable?.let { handler.removeCallbacks(it) }
        notificationUpdateRunnable?.let { handler.removeCallbacks(it) }
        
        // Stop and release MediaRecorder
        mediaRecorder?.apply {
            try {
                stop()
            } catch (e: RuntimeException) {
                Log.w(TAG, "Error stopping MediaRecorder", e)
            }
            release()
        }
        mediaRecorder = null
        currentRecordingFile = null
        
        // Reset counters
        recordingStartTime = 0
        currentSegmentStartTime = 0
        recordingCounter = 0
    }
    
    private fun setupSegmentTimer() {
        val segmentDurationMs = segmentDurationMinutes * 60 * 1000L
        
        segmentRunnable = object : Runnable {
            override fun run() {
                if (isRecording) {
                    try {
                        recordingCounter++
                        startNewRecordingSegment()
                        handler.postDelayed(this, segmentDurationMs)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error during segment transition", e)
                        stopRecording()
                    }
                }
            }
        }
        
        handler.postDelayed(segmentRunnable!!, segmentDurationMs)
    }
    
    private fun setupNotificationUpdateTimer() {
        notificationUpdateRunnable = object : Runnable {
            override fun run() {
                if (isRecording) {
                    val duration = System.currentTimeMillis() - recordingStartTime
                    val formattedDuration = formatDuration(duration)
                    
                    val notification = createNotification(formattedDuration)
                    val notificationManager = getSystemService(NotificationManager::class.java)
                    notificationManager.notify(NOTIFICATION_ID, notification)
                    
                    handler.postDelayed(this, 5000) // Update every 5 seconds
                }
            }
        }
        
        handler.postDelayed(notificationUpdateRunnable!!, 5000)
    }
    
    private fun formatDuration(durationMs: Long): String {
        val seconds = (durationMs / 1000) % 60
        val minutes = (durationMs / (1000 * 60)) % 60
        val hours = (durationMs / (1000 * 60 * 60))
        
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }
    
    // Audio focus management
    private val audioFocusChangeListener = AudioManager.OnAudioFocusChangeListener { focusChange ->
        when (focusChange) {
            AudioManager.AUDIOFOCUS_LOSS -> {
                Log.d(TAG, "Audio focus lost - pausing recording")
                pauseRecording()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                Log.d(TAG, "Audio focus lost temporarily - pausing recording")
                pauseRecording()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                Log.d(TAG, "Audio focus lost (can duck) - continuing recording at lower volume")
                // Continue recording but could lower volume if needed
            }
            AudioManager.AUDIOFOCUS_GAIN -> {
                Log.d(TAG, "Audio focus gained - resuming recording")
                resumeRecording()
            }
        }
    }

    private fun requestAudioFocus(): Boolean {
        return try {
            val result = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val focusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                    .setAudioAttributes(
                        android.media.AudioAttributes.Builder()
                            .setUsage(android.media.AudioAttributes.USAGE_MEDIA)
                            .setContentType(android.media.AudioAttributes.CONTENT_TYPE_SPEECH)
                            .build()
                    )
                    .setAcceptsDelayedFocusGain(true)
                    .setOnAudioFocusChangeListener(audioFocusChangeListener, handler)
                    .build()

                audioFocusRequest = focusRequest
                audioManager.requestAudioFocus(focusRequest)
            } else {
                @Suppress("DEPRECATION")
                audioManager.requestAudioFocus(
                    audioFocusChangeListener,
                    AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN
                )
            }

            val success = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            Log.d(TAG, "Audio focus request result: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting audio focus", e)
            false
        }
    }

    private fun releaseAudioFocus() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                audioFocusRequest?.let { request ->
                    audioManager.abandonAudioFocusRequest(request as AudioFocusRequest)
                }
            } else {
                @Suppress("DEPRECATION")
                audioManager.abandonAudioFocus(audioFocusChangeListener)
            }
            Log.d(TAG, "Audio focus released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing audio focus", e)
        }
    }

    private fun pauseRecording() {
        try {
            mediaRecorder?.let { recorder ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    recorder.pause()
                    Log.d(TAG, "Recording paused")
                } else {
                    // For older Android versions, we need to stop and restart
                    Log.d(TAG, "Pause not supported on this Android version, continuing recording")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error pausing recording", e)
        }
    }

    private fun resumeRecording() {
        try {
            mediaRecorder?.let { recorder ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    recorder.resume()
                    Log.d(TAG, "Recording resumed")
                } else {
                    // For older Android versions, recording was never paused
                    Log.d(TAG, "Resume not needed on this Android version")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error resuming recording", e)
        }
    }

    // Playback audio focus management
    private val playbackAudioFocusChangeListener = AudioManager.OnAudioFocusChangeListener { focusChange ->
        when (focusChange) {
            AudioManager.AUDIOFOCUS_LOSS -> {
                Log.d(TAG, "Playback audio focus lost - stopping playback")
                stopPlayback()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                Log.d(TAG, "Playback audio focus lost temporarily - pausing playback")
                mediaPlayer?.pause()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                Log.d(TAG, "Playback audio focus lost (can duck) - lowering volume")
                mediaPlayer?.setVolume(0.3f, 0.3f)
            }
            AudioManager.AUDIOFOCUS_GAIN -> {
                Log.d(TAG, "Playback audio focus gained - resuming playback")
                mediaPlayer?.let { player ->
                    if (!player.isPlaying) {
                        player.start()
                    }
                    player.setVolume(1.0f, 1.0f)
                }
            }
        }
    }

    private fun requestPlaybackAudioFocus(): Boolean {
        return try {
            val result = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val focusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                    .setAudioAttributes(
                        android.media.AudioAttributes.Builder()
                            .setUsage(android.media.AudioAttributes.USAGE_MEDIA)
                            .setContentType(android.media.AudioAttributes.CONTENT_TYPE_MUSIC)
                            .build()
                    )
                    .setAcceptsDelayedFocusGain(true)
                    .setOnAudioFocusChangeListener(playbackAudioFocusChangeListener, handler)
                    .build()

                playbackAudioFocusRequest = focusRequest
                audioManager.requestAudioFocus(focusRequest)
            } else {
                @Suppress("DEPRECATION")
                audioManager.requestAudioFocus(
                    playbackAudioFocusChangeListener,
                    AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN
                )
            }

            val success = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            Log.d(TAG, "Playback audio focus request result: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting playback audio focus", e)
            false
        }
    }

    private fun releasePlaybackAudioFocus() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                playbackAudioFocusRequest?.let { request ->
                    audioManager.abandonAudioFocusRequest(request as AudioFocusRequest)
                }
            } else {
                @Suppress("DEPRECATION")
                audioManager.abandonAudioFocus(playbackAudioFocusChangeListener)
            }
            playbackAudioFocusRequest = null
            Log.d(TAG, "Playback audio focus released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing playback audio focus", e)
        }
    }

    private fun ensureVolumeForPlayback() {
        try {
            val currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)

            Log.d(TAG, "Current music volume: $currentVolume/$maxVolume")

            // If volume is too low, set it to at least 50% of max
            if (currentVolume < maxVolume * 0.3) {
                val newVolume = (maxVolume * 0.5).toInt()
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, newVolume, 0)
                Log.d(TAG, "Increased music volume to: $newVolume/$maxVolume")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking/setting volume", e)
        }
    }
    
    // Public methods for service communication
    fun isCurrentlyRecording(): Boolean = isRecording
    
    fun getCurrentRecordingInfo(): Map<String, Any> {
        return if (isRecording) {
            val duration = System.currentTimeMillis() - recordingStartTime
            val segmentDuration = System.currentTimeMillis() - currentSegmentStartTime
            
            mapOf(
                "isRecording" to true,
                "totalDuration" to duration,
                "segmentDuration" to segmentDuration,
                "currentSegment" to recordingCounter,
                "currentFilePath" to (currentRecordingFile?.absolutePath ?: ""),
                "segmentDurationMinutes" to segmentDurationMinutes
            )
        } else {
            mapOf("isRecording" to false)
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        if (isRecording) {
            stopRecording()
        }
        stopPlayback()
        releaseWakeLock()
        Log.d(TAG, "AudioRecordingService destroyed")
    }
}
