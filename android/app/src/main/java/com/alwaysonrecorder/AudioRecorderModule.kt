package com.alwaysonrecorder

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.IBinder
import android.util.Log
import androidx.core.content.ContextCompat
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule

class AudioRecorderModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    
    companion object {
        private const val TAG = "AudioRecorderModule"
        private const val MODULE_NAME = "AudioRecorderModule"
        
        // Events
        private const val EVENT_RECORDING_STATUS_CHANGED = "recordingStatusChanged"
        private const val EVENT_SEGMENT_COMPLETED = "segmentCompleted"
    }
    
    private var audioRecordingService: AudioRecordingService? = null
    private var isServiceBound = false
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as AudioRecordingService.AudioRecordingBinder
            audioRecordingService = binder.getService()
            isServiceBound = true
            Log.d(TAG, "Service connected")
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            audioRecordingService = null
            isServiceBound = false
            Log.d(TAG, "Service disconnected")
        }
    }
    
    override fun getName(): String = MODULE_NAME
    
    override fun initialize() {
        super.initialize()
        bindToService()
    }
    
    override fun onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy()
        unbindFromService()
    }
    
    private fun bindToService() {
        val intent = Intent(reactApplicationContext, AudioRecordingService::class.java)
        reactApplicationContext.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }
    
    private fun unbindFromService() {
        if (isServiceBound) {
            reactApplicationContext.unbindService(serviceConnection)
            isServiceBound = false
        }
    }
    
    @ReactMethod
    fun requestMicrophonePermission(promise: Promise) {
        try {
            val permission = Manifest.permission.RECORD_AUDIO
            val hasPermission = ContextCompat.checkSelfPermission(
                reactApplicationContext, 
                permission
            ) == PackageManager.PERMISSION_GRANTED
            
            if (hasPermission) {
                val result = Arguments.createMap().apply {
                    putBoolean("granted", true)
                }
                promise.resolve(result)
            } else {
                // In a real implementation, you would request permission here
                // For now, we'll just return that permission is needed
                promise.reject("PERMISSION_DENIED", "Microphone permission not granted")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking microphone permission", e)
            promise.reject("PERMISSION_ERROR", "Error checking microphone permission: ${e.message}")
        }
    }
    
    @ReactMethod
    fun startRecording(segmentMinutes: Int, promise: Promise) {
        try {
            // Check if we have microphone permission
            val hasPermission = ContextCompat.checkSelfPermission(
                reactApplicationContext,
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED
            
            if (!hasPermission) {
                promise.reject("PERMISSION_DENIED", "Microphone permission not granted")
                return
            }
            
            // Start the service if not already bound
            if (!isServiceBound) {
                bindToService()
                // Wait a moment for service to bind
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    startRecordingInternal(segmentMinutes, promise)
                }, 500)
            } else {
                startRecordingInternal(segmentMinutes, promise)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error starting recording", e)
            promise.reject("RECORDING_ERROR", "Failed to start recording: ${e.message}")
        }
    }
    
    private fun startRecordingInternal(segmentMinutes: Int, promise: Promise) {
        try {
            val service = audioRecordingService
            if (service == null) {
                promise.reject("SERVICE_ERROR", "Audio recording service not available")
                return
            }
            
            if (service.isCurrentlyRecording()) {
                promise.reject("ALREADY_RECORDING", "Recording is already in progress")
                return
            }
            
            // Start the service with recording action
            val intent = Intent(reactApplicationContext, AudioRecordingService::class.java).apply {
                action = AudioRecordingService.ACTION_START_RECORDING
                putExtra(AudioRecordingService.EXTRA_SEGMENT_MINUTES, segmentMinutes)
            }
            
            reactApplicationContext.startForegroundService(intent)
            
            // Give the service a moment to start recording
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val recordingInfo = service.getCurrentRecordingInfo()
                if (recordingInfo["isRecording"] as Boolean) {
                    val result = Arguments.createMap().apply {
                        putBoolean("success", true)
                        putString("filePath", recordingInfo["currentFilePath"] as String)
                    }
                    promise.resolve(result)
                    
                    // Send event to React Native
                    sendEvent(EVENT_RECORDING_STATUS_CHANGED, Arguments.createMap().apply {
                        putBoolean("isRecording", true)
                    })
                } else {
                    promise.reject("RECORDING_ERROR", "Failed to start recording")
                }
            }, 1000)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in startRecordingInternal", e)
            promise.reject("RECORDING_ERROR", "Failed to start recording: ${e.message}")
        }
    }
    
    @ReactMethod
    fun stopRecording(promise: Promise) {
        try {
            val service = audioRecordingService
            if (service == null) {
                promise.reject("SERVICE_ERROR", "Audio recording service not available")
                return
            }
            
            if (!service.isCurrentlyRecording()) {
                promise.reject("NOT_RECORDING", "No recording in progress")
                return
            }
            
            // Stop the service
            val intent = Intent(reactApplicationContext, AudioRecordingService::class.java).apply {
                action = AudioRecordingService.ACTION_STOP_RECORDING
            }
            
            reactApplicationContext.startService(intent)
            
            val result = Arguments.createMap().apply {
                putBoolean("success", true)
            }
            promise.resolve(result)
            
            // Send event to React Native
            sendEvent(EVENT_RECORDING_STATUS_CHANGED, Arguments.createMap().apply {
                putBoolean("isRecording", false)
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping recording", e)
            promise.reject("RECORDING_ERROR", "Failed to stop recording: ${e.message}")
        }
    }
    
    @ReactMethod
    fun getCurrentRecordingInfo(promise: Promise) {
        try {
            val service = audioRecordingService
            if (service == null) {
                promise.reject("SERVICE_ERROR", "Audio recording service not available")
                return
            }
            
            val recordingInfo = service.getCurrentRecordingInfo()
            val result = Arguments.createMap()
            
            recordingInfo.forEach { (key, value) ->
                when (value) {
                    is Boolean -> result.putBoolean(key, value)
                    is Int -> result.putInt(key, value)
                    is Long -> result.putDouble(key, value.toDouble())
                    is String -> result.putString(key, value)
                    is Double -> result.putDouble(key, value)
                }
            }
            
            promise.resolve(result)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting recording info", e)
            promise.reject("INFO_ERROR", "Failed to get recording info: ${e.message}")
        }
    }
    
    @ReactMethod
    fun getNextChunkFilePath(promise: Promise) {
        try {
            val service = audioRecordingService
            if (service == null) {
                promise.reject("SERVICE_ERROR", "Audio recording service not available")
                return
            }

            val recordingInfo = service.getCurrentRecordingInfo()
            val result = Arguments.createMap().apply {
                putString("filePath", recordingInfo["currentFilePath"] as? String ?: "")
                putInt("segmentNumber", recordingInfo["currentSegment"] as? Int ?: 0)
            }

            promise.resolve(result)

        } catch (e: Exception) {
            Log.e(TAG, "Error getting next chunk file path", e)
            promise.reject("FILE_ERROR", "Failed to get next chunk file path: ${e.message}")
        }
    }

    @ReactMethod
    fun getAllRecordingFiles(promise: Promise) {
        try {
            val service = audioRecordingService
            if (service == null) {
                promise.reject("SERVICE_ERROR", "Audio recording service not available")
                return
            }

            val files = service.getAllRecordingFiles()
            val filesArray = Arguments.createArray()

            files.forEach { file ->
                val fileInfo = Arguments.createMap().apply {
                    putString("path", file.absolutePath)
                    putString("name", file.name)
                    putDouble("size", file.length().toDouble())
                    putDouble("lastModified", file.lastModified().toDouble())
                }
                filesArray.pushMap(fileInfo)
            }

            promise.resolve(filesArray)

        } catch (e: Exception) {
            Log.e(TAG, "Error getting recording files", e)
            promise.reject("FILE_ERROR", "Failed to get recording files: ${e.message}")
        }
    }

    @ReactMethod
    fun deleteRecordingFile(filePath: String, promise: Promise) {
        try {
            val service = audioRecordingService
            if (service == null) {
                promise.reject("SERVICE_ERROR", "Audio recording service not available")
                return
            }

            val deleted = service.deleteRecordingFile(filePath)
            val result = Arguments.createMap().apply {
                putBoolean("success", deleted)
            }

            promise.resolve(result)

        } catch (e: Exception) {
            Log.e(TAG, "Error deleting recording file", e)
            promise.reject("FILE_ERROR", "Failed to delete recording file: ${e.message}")
        }
    }

    @ReactMethod
    fun playRecording(filePath: String, promise: Promise) {
        try {
            val service = audioRecordingService
            if (service == null) {
                promise.reject("SERVICE_ERROR", "Audio recording service not available")
                return
            }

            val success = service.startPlayback(filePath)
            val result = Arguments.createMap().apply {
                putBoolean("success", success)
                putString("filePath", filePath)
            }

            if (success) {
                promise.resolve(result)
            } else {
                promise.reject("PLAYBACK_ERROR", "Failed to start playback")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error starting playback", e)
            promise.reject("PLAYBACK_ERROR", "Failed to start playback: ${e.message}")
        }
    }

    @ReactMethod
    fun stopPlayback(promise: Promise) {
        try {
            val service = audioRecordingService
            if (service == null) {
                promise.reject("SERVICE_ERROR", "Audio recording service not available")
                return
            }

            service.stopPlayback()
            val result = Arguments.createMap().apply {
                putBoolean("success", true)
            }

            promise.resolve(result)

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping playback", e)
            promise.reject("PLAYBACK_ERROR", "Failed to stop playback: ${e.message}")
        }
    }

    @ReactMethod
    fun getPlaybackStatus(promise: Promise) {
        try {
            val service = audioRecordingService
            if (service == null) {
                promise.reject("SERVICE_ERROR", "Audio recording service not available")
                return
            }

            val status = service.getPlaybackStatus()
            val result = Arguments.createMap()

            status.forEach { (key, value) ->
                when (value) {
                    is Boolean -> result.putBoolean(key, value)
                    is Int -> result.putInt(key, value)
                    is Long -> result.putDouble(key, value.toDouble())
                    is String -> result.putString(key, value)
                    is Double -> result.putDouble(key, value)
                    is Float -> result.putDouble(key, value.toDouble())
                }
            }

            promise.resolve(result)

        } catch (e: Exception) {
            Log.e(TAG, "Error getting playback status", e)
            promise.reject("PLAYBACK_ERROR", "Failed to get playback status: ${e.message}")
        }
    }
    
    private fun sendEvent(eventName: String, params: WritableMap?) {
        reactApplicationContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, params)
    }
    
    // Expose constants to React Native
    override fun getConstants(): MutableMap<String, Any> {
        return hashMapOf(
            "EVENTS" to hashMapOf(
                "RECORDING_STATUS_CHANGED" to EVENT_RECORDING_STATUS_CHANGED,
                "SEGMENT_COMPLETED" to EVENT_SEGMENT_COMPLETED
            )
        )
    }
}
