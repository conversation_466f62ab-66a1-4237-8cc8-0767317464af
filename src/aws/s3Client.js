// src/aws/s3Client.js
// AWS SDK v2 S3 client configuration

// Import polyfills first
import 'react-native-get-random-values';

import AWS from 'aws-sdk';

// Configuration constants
export const S3_CONFIG = {
  BUCKET_NAME: 'audio-recorder-poc',
  REGION: 'ap-south-1', // Correct region based on your S3 bucket
  USER_ID: 'mubarak',
};

// AWS Credential Options - Choose one:

// Option 1: Use environment variables (PRODUCTION RECOMMENDED)
const AWS_CREDENTIALS = {
  accessKeyId: process.env.AWS_ACCESS_KEY_ID || '********************',
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'kip6j19p1eETOhIP7iRxJFzU7hpc4+99iIXhrRkL',
};

// Option 2: Use hardcoded credentials (DEVELOPMENT ONLY)
// const AWS_CREDENTIALS = {
//   accessKeyId: 'PASTE_YOUR_ACCESS_KEY_HERE',
//   secretAccessKey: 'PASTE_YOUR_SECRET_KEY_HERE',
// };

// Option 3: Use temporary credentials (for testing)
// const AWS_CREDENTIALS = {
//   accessKeyId: '********************',
//   secretAccessKey: 'LOcgG9+2wJ2vYhghrOwvkcmm8S+AZ8saWfwqnwx1',
// };

// Configure AWS SDK v2
AWS.config.update({
  region: S3_CONFIG.REGION,
  ...AWS_CREDENTIALS,
});

// Create S3 client
export const s3 = new AWS.S3();

console.log('✅ S3 Client initialized');
console.log(`Region: ${S3_CONFIG.REGION}`);
console.log(`Bucket: ${S3_CONFIG.BUCKET_NAME}`);
console.log(`Auth: Hardcoded (testing only)`);

/**
 * Test AWS credentials by listing bucket contents
 */
export async function testCredentials() {
  try {
    console.log('🔐 Testing AWS credentials...');
    await s3.listObjects({
      Bucket: S3_CONFIG.BUCKET_NAME,
      MaxKeys: 1
    }).promise();

    console.log('✅ AWS credentials are valid!');
    console.log(`📁 Bucket accessible: ${S3_CONFIG.BUCKET_NAME}`);
    return true;
  } catch (error) {
    console.error('❌ AWS credentials test failed:', error.message);
    console.error('🔧 Possible solutions:');
    console.error('  1. Update credentials in s3Client.js');
    console.error('  2. Set up AWS Cognito Identity Pool');
    console.error('  3. Use AWS CLI to configure credentials');
    return false;
  }
}

/**
 * Comprehensive S3 connectivity and permissions test
 */
export async function testS3Connectivity() {
  console.log('\n🔍 [S3 TEST] Starting comprehensive S3 connectivity test');
  console.log('=======================================================');

  const testResults = {
    credentialsValid: false,
    bucketAccessible: false,
    canListObjects: false,
    canCreateMultipart: false,
    canUploadPart: false,
    canCompleteMultipart: false,
    canPutObject: false,
    errors: []
  };

  try {
    // Test 1: Basic credentials
    console.log('🧪 Test 1: Basic credentials validation');
    testResults.credentialsValid = await testCredentials();

    // Test 2: Bucket access
    console.log('🧪 Test 2: Bucket access test');
    try {
      const listResult = await s3.listObjects({
        Bucket: S3_CONFIG.BUCKET_NAME,
        MaxKeys: 5,
        Prefix: 'recordings/'
      }).promise();

      testResults.bucketAccessible = true;
      testResults.canListObjects = true;
      console.log(`✅ Bucket accessible. Found ${listResult.Contents?.length || 0} objects`);

      if (listResult.Contents && listResult.Contents.length > 0) {
        console.log('📁 Recent objects:');
        listResult.Contents.slice(0, 3).forEach(obj => {
          console.log(`  - ${obj.Key} (${obj.Size} bytes, ${obj.LastModified})`);
        });
      }
    } catch (error) {
      testResults.errors.push(`Bucket access failed: ${error.message}`);
      console.error('❌ Bucket access failed:', error.message);
    }

    // Test 3: Multipart upload creation
    console.log('🧪 Test 3: Multipart upload creation test');
    let testUploadId = null;
    const testKey = `recordings/${S3_CONFIG.USER_ID}/test-${Date.now()}/test-multipart.m4a`;

    try {
      const createResult = await s3.createMultipartUpload({
        Bucket: S3_CONFIG.BUCKET_NAME,
        Key: testKey,
        ContentType: 'audio/m4a'
      }).promise();

      testUploadId = createResult.UploadId;
      testResults.canCreateMultipart = true;
      console.log(`✅ Multipart upload created: ${testUploadId}`);

      // Test 4: Upload a test part
      console.log('🧪 Test 4: Upload part test');
      try {
        const testData = Buffer.from('test audio data for multipart upload');
        const partResult = await s3.uploadPart({
          Bucket: S3_CONFIG.BUCKET_NAME,
          Key: testKey,
          PartNumber: 1,
          UploadId: testUploadId,
          Body: testData
        }).promise();

        testResults.canUploadPart = true;
        console.log(`✅ Part uploaded: ${partResult.ETag}`);

        // Test 5: Complete multipart upload
        console.log('🧪 Test 5: Complete multipart upload test');
        try {
          const completeResult = await s3.completeMultipartUpload({
            Bucket: S3_CONFIG.BUCKET_NAME,
            Key: testKey,
            UploadId: testUploadId,
            MultipartUpload: {
              Parts: [{ ETag: partResult.ETag, PartNumber: 1 }]
            }
          }).promise();

          testResults.canCompleteMultipart = true;
          console.log(`✅ Multipart upload completed: ${completeResult.Location}`);

          // Clean up test file
          try {
            await s3.deleteObject({
              Bucket: S3_CONFIG.BUCKET_NAME,
              Key: testKey
            }).promise();
            console.log('🧹 Test file cleaned up');
          } catch (cleanupError) {
            console.warn('⚠️ Failed to clean up test file:', cleanupError.message);
          }

        } catch (error) {
          testResults.errors.push(`Complete multipart failed: ${error.message}`);
          console.error('❌ Complete multipart failed:', error.message);

          // Try to abort the upload
          try {
            await s3.abortMultipartUpload({
              Bucket: S3_CONFIG.BUCKET_NAME,
              Key: testKey,
              UploadId: testUploadId
            }).promise();
            console.log('🛑 Aborted test multipart upload');
          } catch (abortError) {
            console.error('❌ Failed to abort test upload:', abortError.message);
          }
        }

      } catch (error) {
        testResults.errors.push(`Upload part failed: ${error.message}`);
        console.error('❌ Upload part failed:', error.message);

        // Abort the upload
        try {
          await s3.abortMultipartUpload({
            Bucket: S3_CONFIG.BUCKET_NAME,
            Key: testKey,
            UploadId: testUploadId
          }).promise();
        } catch (abortError) {
          console.error('❌ Failed to abort test upload:', abortError.message);
        }
      }

    } catch (error) {
      testResults.errors.push(`Create multipart failed: ${error.message}`);
      console.error('❌ Create multipart failed:', error.message);
    }

    // Test 6: Simple putObject test
    console.log('🧪 Test 6: Simple putObject test');
    const simpleTestKey = `recordings/${S3_CONFIG.USER_ID}/test-${Date.now()}/simple-test.json`;
    try {
      const putResult = await s3.putObject({
        Bucket: S3_CONFIG.BUCKET_NAME,
        Key: simpleTestKey,
        Body: JSON.stringify({ test: true, timestamp: new Date().toISOString() }),
        ContentType: 'application/json'
      }).promise();

      testResults.canPutObject = true;
      console.log(`✅ Simple putObject succeeded: ${putResult.ETag}`);

      // Clean up
      try {
        await s3.deleteObject({
          Bucket: S3_CONFIG.BUCKET_NAME,
          Key: simpleTestKey
        }).promise();
        console.log('🧹 Simple test file cleaned up');
      } catch (cleanupError) {
        console.warn('⚠️ Failed to clean up simple test file:', cleanupError.message);
      }

    } catch (error) {
      testResults.errors.push(`PutObject failed: ${error.message}`);
      console.error('❌ PutObject failed:', error.message);
    }

  } catch (error) {
    testResults.errors.push(`General test failure: ${error.message}`);
    console.error('❌ General test failure:', error);
  }

  // Summary
  console.log('\n📊 S3 Connectivity Test Results:');
  console.log('================================');
  console.log(`✅ Credentials Valid: ${testResults.credentialsValid}`);
  console.log(`✅ Bucket Accessible: ${testResults.bucketAccessible}`);
  console.log(`✅ Can List Objects: ${testResults.canListObjects}`);
  console.log(`✅ Can Create Multipart: ${testResults.canCreateMultipart}`);
  console.log(`✅ Can Upload Part: ${testResults.canUploadPart}`);
  console.log(`✅ Can Complete Multipart: ${testResults.canCompleteMultipart}`);
  console.log(`✅ Can Put Object: ${testResults.canPutObject}`);

  if (testResults.errors.length > 0) {
    console.log('\n❌ Errors encountered:');
    testResults.errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }

  console.log('================================');

  return testResults;
}
