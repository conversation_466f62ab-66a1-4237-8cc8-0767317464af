// src/aws/__tests__/multipartUpload.test.js
import {
  startSessionMultipartUpload,
  uploadSessionChunk,
  finishSessionMultipartUpload,
  uploadMultipartManifest,
  abortSessionMultipartUpload,
  getSessionMultipartState,
  getActiveSessionUploads,
  clearAllSessionUploads
} from '../uploadChunk';

// Mock AWS SDK
jest.mock('../s3Client', () => ({
  s3: {
    createMultipartUpload: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        UploadId: 'test-upload-id-123',
        Key: 'recordings/test-user/test-session/recording-test-session.m4a'
      })
    }),
    uploadPart: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        ETag: '"test-etag-123"'
      })
    }),
    completeMultipartUpload: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        Location: 'https://test-bucket.s3.amazonaws.com/recordings/test-user/test-session/recording-test-session.m4a',
        ETag: '"final-etag-456"'
      })
    }),
    abortMultipartUpload: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    }),
    putObject: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        ETag: '"manifest-etag-789"'
      })
    })
  },
  S3_CONFIG: {
    BUCKET_NAME: 'test-bucket',
    USER_ID: 'test-user'
  }
}));

// Mock React Native FS
jest.mock('react-native-fs', () => ({
  readFile: jest.fn().mockResolvedValue('dGVzdCBhdWRpbyBkYXRh') // base64 for "test audio data"
}));

describe('Multipart Upload Functionality', () => {
  const testUserId = 'test-user';
  const testSessionId = 'test-session-123';
  const testFilePath = '/test/path/chunk.m4a';

  beforeEach(() => {
    jest.clearAllMocks();
    clearAllSessionUploads(); // Clear session state between tests
  });

  describe('startSessionMultipartUpload', () => {
    it('should start a new multipart upload session', async () => {
      const uploadId = await startSessionMultipartUpload(testUserId, testSessionId);
      
      expect(uploadId).toBe('test-upload-id-123');
      
      const state = getSessionMultipartState(testSessionId);
      expect(state).toEqual({
        uploadId: 'test-upload-id-123',
        key: `recordings/${testUserId}/${testSessionId}/recording-${testSessionId}.m4a`,
        partsCount: 0,
        parts: [],
        userId: testUserId
      });
    });

    it('should track active sessions', async () => {
      await startSessionMultipartUpload(testUserId, testSessionId);
      
      const activeSessions = getActiveSessionUploads();
      expect(activeSessions).toHaveLength(1);
      expect(activeSessions[0]).toEqual({
        sessionId: testSessionId,
        uploadId: 'test-upload-id-123',
        key: `recordings/${testUserId}/${testSessionId}/recording-${testSessionId}.m4a`,
        partsCount: 0,
        userId: testUserId
      });
    });
  });

  describe('uploadSessionChunk', () => {
    beforeEach(async () => {
      await startSessionMultipartUpload(testUserId, testSessionId);
    });

    it('should upload a chunk as part of multipart upload', async () => {
      const result = await uploadSessionChunk(testFilePath, testSessionId, 0);
      
      expect(result).toEqual({
        success: true,
        chunkIndex: 0,
        partNumber: 1,
        etag: '"test-etag-123"',
        size: 15, // length of "test audio data"
        sessionId: testSessionId
      });
      
      const state = getSessionMultipartState(testSessionId);
      expect(state.partsCount).toBe(1);
      expect(state.parts[0]).toEqual({
        ETag: '"test-etag-123"',
        PartNumber: 1
      });
    });

    it('should handle multiple chunks with correct part numbers', async () => {
      await uploadSessionChunk(testFilePath, testSessionId, 0);
      await uploadSessionChunk(testFilePath, testSessionId, 1);
      await uploadSessionChunk(testFilePath, testSessionId, 2);
      
      const state = getSessionMultipartState(testSessionId);
      expect(state.partsCount).toBe(3);
      expect(state.parts.map(p => p.PartNumber)).toEqual([1, 2, 3]);
    });

    it('should throw error if no active session', async () => {
      await expect(uploadSessionChunk(testFilePath, 'non-existent-session', 0))
        .rejects.toThrow('No active multipart upload for session non-existent-session');
    });
  });

  describe('finishSessionMultipartUpload', () => {
    beforeEach(async () => {
      await startSessionMultipartUpload(testUserId, testSessionId);
      await uploadSessionChunk(testFilePath, testSessionId, 0);
      await uploadSessionChunk(testFilePath, testSessionId, 1);
    });

    it('should complete multipart upload and clean up session', async () => {
      const result = await finishSessionMultipartUpload(testSessionId);
      
      expect(result).toEqual({
        success: true,
        sessionId: testSessionId,
        finalKey: `recordings/${testUserId}/${testSessionId}/recording-${testSessionId}.m4a`,
        location: 'https://test-bucket.s3.amazonaws.com/recordings/test-user/test-session/recording-test-session.m4a',
        etag: '"final-etag-456"',
        totalParts: 2,
        totalSize: 10485760, // 2 * 5MB
        userId: testUserId
      });
      
      // Session should be cleaned up
      expect(getSessionMultipartState(testSessionId)).toBeNull();
      expect(getActiveSessionUploads()).toHaveLength(0);
    });

    it('should throw error if no active session', async () => {
      await expect(finishSessionMultipartUpload('non-existent-session'))
        .rejects.toThrow('No active multipart upload for session non-existent-session');
    });

    it('should throw error if no parts uploaded', async () => {
      await startSessionMultipartUpload(testUserId, 'empty-session');
      
      await expect(finishSessionMultipartUpload('empty-session'))
        .rejects.toThrow('No parts uploaded for session empty-session');
    });
  });

  describe('uploadMultipartManifest', () => {
    it('should upload manifest for completed multipart upload', async () => {
      const multipartResult = {
        finalKey: `recordings/${testUserId}/${testSessionId}/recording-${testSessionId}.m4a`,
        location: 'https://test-bucket.s3.amazonaws.com/test-file.m4a',
        etag: '"final-etag-456"',
        totalParts: 2,
        totalSize: 10485760
      };
      
      const result = await uploadMultipartManifest(testUserId, testSessionId, multipartResult);
      
      expect(result).toEqual({
        success: true,
        key: `recordings/${testUserId}/${testSessionId}/manifest.json`,
        etag: '"manifest-etag-789"'
      });
    });
  });

  describe('abortSessionMultipartUpload', () => {
    beforeEach(async () => {
      await startSessionMultipartUpload(testUserId, testSessionId);
    });

    it('should abort multipart upload and clean up session', async () => {
      const result = await abortSessionMultipartUpload(testSessionId);
      
      expect(result).toBe(true);
      expect(getSessionMultipartState(testSessionId)).toBeNull();
      expect(getActiveSessionUploads()).toHaveLength(0);
    });

    it('should return true for non-existent session', async () => {
      const result = await abortSessionMultipartUpload('non-existent-session');
      expect(result).toBe(true);
    });
  });
});
