// src/aws/uploadChunk.js
// AWS SDK v2 implementation with multipart upload
import RNFS from 'react-native-fs';
import { s3, S3_CONFIG } from './s3Client';
import { completeMultipartUpload } from './completeMultipart';

// Re-export S3_CONFIG for backward compatibility
export { S3_CONFIG };

// Global variables to track multipart upload state
let currentUploadId = null;
let currentUploadKey = null;
let uploadedParts = [];

// Session-based multipart upload tracking
let sessionMultipartUploads = new Map(); // sessionId -> { uploadId, key, parts, userId }

/**
 * Gets the current multipart upload state for debugging
 * @returns {Object} Current state
 */
export function getMultipartState() {
  return {
    uploadId: currentUploadId,
    key: currentUploadKey,
    partsCount: uploadedParts.length,
    parts: uploadedParts
  };
}

/**
 * Gets the session-based multipart upload state for debugging
 * @param {string} sessionId - Session identifier
 * @returns {Object} Session state
 */
export function getSessionMultipartState(sessionId) {
  const sessionData = sessionMultipartUploads.get(sessionId);
  return sessionData ? {
    uploadId: sessionData.uploadId,
    key: sessionData.key,
    partsCount: sessionData.parts.length,
    parts: sessionData.parts,
    userId: sessionData.userId
  } : null;
}

/**
 * Starts a new session-based multipart upload for size-based segmentation
 * @param {string} userId - User identifier
 * @param {string} sessionId - Recording session timestamp
 * @returns {Promise<string>} Upload ID
 */
export async function startSessionMultipartUpload(userId, sessionId) {
  // Generate key for the final consolidated file
  const finalKey = `recordings/${userId}/${sessionId}/recording-${sessionId}.m4a`;

  console.log(`\n� [DEBUG] Starting startSessionMultipartUpload`);
  console.log(`�🚀 Starting session multipart upload for: ${finalKey}`);
  console.log(`📋 Session ID: ${sessionId}`);
  console.log(`👤 User ID: ${userId}`);
  console.log(`🪣 Bucket: ${S3_CONFIG.BUCKET_NAME}`);

  try {
    const params = {
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: finalKey,
      ContentType: 'audio/m4a',
    };

    console.log(`🔍 [DEBUG] CreateMultipartUpload params:`, params);
    console.log(`🔍 [DEBUG] Calling s3.createMultipartUpload...`);

    const result = await s3.createMultipartUpload(params).promise();
    console.log(`🔍 [DEBUG] s3.createMultipartUpload result:`, result);

    // Store session-specific upload state
    const sessionData = {
      uploadId: result.UploadId,
      key: finalKey,
      parts: [],
      userId: userId
    };

    sessionMultipartUploads.set(sessionId, sessionData);
    console.log(`🔍 [DEBUG] Session data stored:`, sessionData);
    console.log(`🔍 [DEBUG] Total active sessions:`, sessionMultipartUploads.size);

    console.log(`✅ Session multipart upload started successfully!`);
    console.log(`  UploadId: ${result.UploadId}`);
    console.log(`  Key: ${finalKey}`);
    console.log(`  Bucket: ${S3_CONFIG.BUCKET_NAME}`);
    console.log(`  Session: ${sessionId}`);

    return result.UploadId;
  } catch (error) {
    console.error('❌ Failed to start session multipart upload:', error);
    console.error(`🔍 [DEBUG] Error details:`, {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.statusCode
    });
    throw error;
  }
}

/**
 * Starts a new multipart upload session
 * @param {string} key - S3 key for the final combined file
 * @returns {Promise<string>} Upload ID
 */
export async function startMultipartUpload(key) {
  console.log(`� Starting multipart upload for: ${key}`);

  try {
    const params = {
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: key,
      ContentType: 'audio/m4a',
    };

    const result = await s3.createMultipartUpload(params).promise();
    currentUploadId = result.UploadId;
    currentUploadKey = key;
    uploadedParts = [];

    console.log(`✅ Multipart upload started successfully!`);
    console.log(`  UploadId: ${currentUploadId}`);
    console.log(`  Key: ${currentUploadKey}`);
    console.log(`  Bucket: ${S3_CONFIG.BUCKET_NAME}`);
    return currentUploadId;
  } catch (error) {
    console.error('❌ Failed to start multipart upload:', error);
    throw error;
  }
}

/**
 * Uploads one audio chunk as a part of the multipart upload.
 * @param {string} localUri    – file://… path from native recorder
 * @param {number} partNumber  – Part number (1-based) for this chunk
 * @param {(p)=>void} onProgress – optional callback with { loaded, total }
 * @returns {Promise<Object>} Upload result with ETag and PartNumber
 */
export async function uploadChunk(localUri, partNumber, onProgress) {
  try {
    if (!currentUploadId || !currentUploadKey) {
      console.error('❌ Upload state check failed:');
      console.error(`  currentUploadId: ${currentUploadId}`);
      console.error(`  currentUploadKey: ${currentUploadKey}`);
      console.error(`  uploadedParts: ${uploadedParts.length}`);
      throw new Error('No active multipart upload. Call startMultipartUpload() first.');
    }

    console.log(`\n� Uploading part ${partNumber} for ${currentUploadKey}`);

    // Read the file as base64
    const fileData = await RNFS.readFile(localUri, 'base64');
    const fileBuffer = Buffer.from(fileData, 'base64');

    console.log(`📁 File read successfully. Size: ${fileBuffer.length} bytes`);

    // Simulate progress if callback provided
    if (onProgress) {
      onProgress({ loaded: 0, total: fileBuffer.length });
    }

    // Upload part using AWS SDK v2
    const params = {
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: currentUploadKey,
      PartNumber: partNumber,
      UploadId: currentUploadId,
      Body: fileBuffer,
    };

    const result = await s3.uploadPart(params).promise();

    console.log(`📡 Part ${partNumber} upload response: ETag=${result.ETag}`);

    // Store the part info for later completion
    const partInfo = { ETag: result.ETag, PartNumber: partNumber };
    uploadedParts.push(partInfo);

    console.log(`✅ SUCCESS: Uploaded part ${partNumber} to S3`);

    // Simulate completion progress
    if (onProgress) {
      onProgress({ loaded: fileBuffer.length, total: fileBuffer.length });
    }

    return { success: true, partNumber, etag: result.ETag };
  } catch (error) {
    console.error(`❌ Failed to upload part ${partNumber}:`, error);
    throw error;
  }
}

/**
 * Completes the multipart upload by combining all parts
 * @returns {Promise<Object>} Final upload result
 */
export async function finishMultipartUpload() {
  try {
    if (!currentUploadId || !currentUploadKey || uploadedParts.length === 0) {
      throw new Error('No active multipart upload or no parts uploaded.');
    }

    console.log(`🏁 Finishing multipart upload for ${currentUploadKey}`);
    console.log(`📦 Total parts to combine: ${uploadedParts.length}`);

    const result = await completeMultipartUpload(
      S3_CONFIG.BUCKET_NAME,
      currentUploadKey,
      currentUploadId,
      uploadedParts
    );

    // Reset state
    currentUploadId = null;
    currentUploadKey = null;
    uploadedParts = [];

    return result;
  } catch (error) {
    console.error('❌ Failed to finish multipart upload:', error);
    throw error;
  }
}

/**
 * Generates S3 key for a recording chunk
 * @param {string} userId - User identifier
 * @param {string} sessionId - Recording session timestamp
 * @param {number} chunkIndex - Chunk sequence number
 * @param {string} chunkTimestamp - Chunk creation timestamp
 * @returns {string} S3 key path
 */
export function generateS3Key(userId, sessionId, chunkIndex, chunkTimestamp) {
  const pad = (n) => String(n).padStart(4, '0');
  return `recordings/${userId}/${sessionId}/chunk-${pad(chunkIndex)}_${chunkTimestamp}.m4a`;
}

/**
 * Uploads a manifest file for the recording session
 * @param {string} userId - User identifier
 * @param {string} sessionId - Recording session timestamp
 * @param {Array} chunks - Array of chunk metadata
 */
export async function uploadManifest(userId, sessionId, chunks) {
  const manifest = {
    userId,
    sessionId,
    startTime: sessionId,
    totalChunks: chunks.length,
    chunks: chunks.map((chunk, index) => ({
      index,
      key: chunk.key,
      timestamp: chunk.timestamp,
      size: chunk.size
    })),
    createdAt: new Date().toISOString()
  };

  const manifestKey = `recordings/${userId}/${sessionId}/manifest.json`;
  console.log(`📄 Uploading manifest to ${manifestKey}:`, manifest);

  try {
    const params = {
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: manifestKey,
      Body: JSON.stringify(manifest, null, 2),
      ContentType: 'application/json',
    };

    const result = await s3.putObject(params).promise();
    console.log(`✅ Manifest uploaded successfully: ${manifestKey}`);
    return { success: true, key: manifestKey, etag: result.ETag };
  } catch (error) {
    console.error(`❌ Failed to upload manifest:`, error);
    throw error;
  }
}

/**
 * Uploads a chunk as part of a session-based multipart upload
 * @param {string} localUri - file://… path from native recorder
 * @param {string} sessionId - Recording session timestamp
 * @param {number} chunkIndex - Chunk sequence number (0-based)
 * @param {(p)=>void} onProgress - optional callback with { loaded, total }
 * @returns {Promise<Object>} Upload result with ETag and PartNumber
 */
export async function uploadSessionChunk(localUri, sessionId, chunkIndex, onProgress) {
  try {
    console.log(`\n🔍 [DEBUG] Starting uploadSessionChunk for session: ${sessionId}, chunk: ${chunkIndex}`);
    console.log(`🔍 [DEBUG] Local file URI: ${localUri}`);

    const sessionData = sessionMultipartUploads.get(sessionId);
    console.log(`🔍 [DEBUG] Session data found:`, sessionData ? 'YES' : 'NO');

    if (!sessionData) {
      console.error(`❌ [DEBUG] No session data found for ${sessionId}`);
      console.error(`🔍 [DEBUG] Available sessions:`, Array.from(sessionMultipartUploads.keys()));
      throw new Error(`No active multipart upload for session ${sessionId}. Call startSessionMultipartUpload() first.`);
    }

    // Part numbers are 1-based in S3
    const partNumber = chunkIndex + 1;

    console.log(`\n🔄 Uploading session chunk ${chunkIndex} (part ${partNumber}) for session ${sessionId}`);
    console.log(`📋 Upload ID: ${sessionData.uploadId}`);
    console.log(`🔑 Key: ${sessionData.key}`);
    console.log(`🪣 Bucket: ${S3_CONFIG.BUCKET_NAME}`);

    // Read the file as base64
    console.log(`🔍 [DEBUG] Reading file: ${localUri}`);
    const fileData = await RNFS.readFile(localUri, 'base64');
    const fileBuffer = Buffer.from(fileData, 'base64');

    console.log(`📁 File read successfully. Size: ${fileBuffer.length} bytes (${(fileBuffer.length / 1024 / 1024).toFixed(2)} MB)`);
    console.log(`🔍 [DEBUG] File buffer created, length: ${fileBuffer.length}`);

    // Simulate progress if callback provided
    if (onProgress) {
      onProgress({ loaded: 0, total: fileBuffer.length });
    }

    // Upload part using AWS SDK v2
    const params = {
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: sessionData.key,
      PartNumber: partNumber,
      UploadId: sessionData.uploadId,
      Body: fileBuffer,
    };

    console.log(`🔍 [DEBUG] Upload params:`, {
      Bucket: params.Bucket,
      Key: params.Key,
      PartNumber: params.PartNumber,
      UploadId: params.UploadId,
      BodySize: params.Body.length
    });

    console.log(`🔍 [DEBUG] Calling s3.uploadPart...`);
    const result = await s3.uploadPart(params).promise();
    console.log(`🔍 [DEBUG] s3.uploadPart result:`, result);

    console.log(`📡 Session chunk ${chunkIndex} upload response: ETag=${result.ETag}`);

    // Store the part info for later completion
    const partInfo = { ETag: result.ETag, PartNumber: partNumber };
    sessionData.parts.push(partInfo);

    console.log(`✅ SUCCESS: Uploaded session chunk ${chunkIndex} as part ${partNumber}`);
    console.log(`📦 Total parts uploaded for session: ${sessionData.parts.length}`);
    console.log(`🔍 [DEBUG] Current parts:`, sessionData.parts);

    // Simulate completion progress
    if (onProgress) {
      onProgress({ loaded: fileBuffer.length, total: fileBuffer.length });
    }

    const uploadResult = {
      success: true,
      chunkIndex,
      partNumber,
      etag: result.ETag,
      size: fileBuffer.length,
      sessionId
    };

    console.log(`🔍 [DEBUG] Upload result:`, uploadResult);
    return uploadResult;
  } catch (error) {
    console.error(`❌ Failed to upload session chunk ${chunkIndex}:`, error);
    console.error(`🔍 [DEBUG] Error details:`, {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.statusCode
    });

    // Handle specific error cases
    if (error?.code === 'NoSuchUpload') {
      console.warn(`⚠️ [RACE CONDITION] Multipart upload already completed for session ${sessionId}`);
      console.warn(`🔍 [RACE CONDITION] This chunk (${chunkIndex}) arrived after completion - this is expected for final chunks`);

      // Return a success-like result to prevent retry loops
      return {
        success: false,
        chunkIndex,
        partNumber: chunkIndex + 1,
        etag: 'already-completed',
        size: 0,
        sessionId,
        raceCondition: true
      };
    }

    throw error;
  }
}

/**
 * Uploads a single audio chunk file directly to S3 (for size-based segmentation)
 * @param {string} localUri - file://… path from native recorder
 * @param {string} userId - User identifier
 * @param {string} sessionId - Recording session timestamp
 * @param {number} chunkIndex - Chunk sequence number
 * @param {string} chunkTimestamp - Chunk creation timestamp
 * @param {(p)=>void} onProgress - optional callback with { loaded, total }
 * @returns {Promise<Object>} Upload result with S3 key and ETag
 */
export async function uploadSingleChunk(localUri, userId, sessionId, chunkIndex, chunkTimestamp, onProgress) {
  try {
    console.log(`📦 Uploading single chunk ${chunkIndex} for session ${sessionId}`);

    // Read the file as base64
    const fileData = await RNFS.readFile(localUri, 'base64');
    const fileBuffer = Buffer.from(fileData, 'base64');

    console.log(`📁 File read successfully. Size: ${fileBuffer.length} bytes (${(fileBuffer.length / 1024 / 1024).toFixed(2)} MB)`);

    // Generate S3 key for this chunk
    const s3Key = generateSingleChunkKey(userId, sessionId, chunkIndex, chunkTimestamp);

    // Simulate progress if callback provided
    if (onProgress) {
      onProgress({ loaded: 0, total: fileBuffer.length });
    }

    // Upload using single putObject call
    const params = {
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: s3Key,
      Body: fileBuffer,
      ContentType: 'audio/m4a',
    };

    const result = await s3.putObject(params).promise();

    console.log(`📡 Single chunk upload response: ETag=${result.ETag}`);
    console.log(`✅ SUCCESS: Uploaded chunk ${chunkIndex} to S3 at ${s3Key}`);

    // Simulate completion progress
    if (onProgress) {
      onProgress({ loaded: fileBuffer.length, total: fileBuffer.length });
    }

    return {
      success: true,
      chunkIndex,
      s3Key,
      etag: result.ETag,
      size: fileBuffer.length,
      timestamp: chunkTimestamp
    };
  } catch (error) {
    console.error(`❌ Failed to upload single chunk ${chunkIndex}:`, error);
    throw error;
  }
}

/**
 * Generates S3 key for a single recording chunk (size-based segmentation)
 * @param {string} userId - User identifier
 * @param {string} sessionId - Recording session timestamp
 * @param {number} chunkIndex - Chunk sequence number
 * @param {string} chunkTimestamp - Chunk creation timestamp
 * @returns {string} S3 key path
 */
export function generateSingleChunkKey(userId, sessionId, chunkIndex, chunkTimestamp) {
  const pad = (n) => String(n).padStart(4, '0');
  return `recordings/${userId}/${sessionId}/chunk-${pad(chunkIndex)}-${chunkTimestamp}.m4a`;
}

/**
 * Completes the session-based multipart upload by combining all parts
 * @param {string} sessionId - Recording session timestamp
 * @returns {Promise<Object>} Final upload result with consolidated file info
 */
export async function finishSessionMultipartUpload(sessionId) {
  try {
    console.log(`\n🔍 [DEBUG] Starting finishSessionMultipartUpload for session: ${sessionId}`);

    const sessionData = sessionMultipartUploads.get(sessionId);
    console.log(`🔍 [DEBUG] Session data found:`, sessionData ? 'YES' : 'NO');

    if (!sessionData) {
      console.error(`❌ [DEBUG] No session data found for ${sessionId}`);
      console.error(`🔍 [DEBUG] Available sessions:`, Array.from(sessionMultipartUploads.keys()));
      throw new Error(`No active multipart upload for session ${sessionId}.`);
    }

    console.log(`🔍 [DEBUG] Session data details:`, {
      uploadId: sessionData.uploadId,
      key: sessionData.key,
      partsCount: sessionData.parts.length,
      userId: sessionData.userId
    });

    if (sessionData.parts.length === 0) {
      console.error(`❌ [DEBUG] No parts found for session ${sessionId}`);
      throw new Error(`No parts uploaded for session ${sessionId}.`);
    }

    console.log(`🏁 Finishing session multipart upload for session ${sessionId}`);
    console.log(`📦 Total parts to combine: ${sessionData.parts.length}`);
    console.log(`🔑 Final file key: ${sessionData.key}`);
    console.log(`🆔 Upload ID: ${sessionData.uploadId}`);
    console.log(`🪣 Bucket: ${S3_CONFIG.BUCKET_NAME}`);

    // Validate and sort parts before completion
    console.log(`🔍 [VALIDATION] Validating parts before completion...`);

    // Sort parts by PartNumber to ensure correct order
    const sortedParts = [...sessionData.parts].sort((a, b) => a.PartNumber - b.PartNumber);

    // Validate sequential part numbering
    const expectedParts = [];
    for (let i = 1; i <= sortedParts.length; i++) {
      expectedParts.push(i);
    }

    const actualPartNumbers = sortedParts.map(p => p.PartNumber);
    const missingParts = expectedParts.filter(expected => !actualPartNumbers.includes(expected));
    const extraParts = actualPartNumbers.filter(actual => !expectedParts.includes(actual));

    if (missingParts.length > 0) {
      console.error(`❌ [VALIDATION] Missing parts: ${missingParts.join(', ')}`);
      throw new Error(`Missing parts in multipart upload: ${missingParts.join(', ')}`);
    }

    if (extraParts.length > 0) {
      console.warn(`⚠️ [VALIDATION] Unexpected extra parts: ${extraParts.join(', ')}`);
    }

    // Validate ETags
    const invalidETags = sortedParts.filter(part => !part.ETag || !part.ETag.startsWith('"'));
    if (invalidETags.length > 0) {
      console.error(`❌ [VALIDATION] Invalid ETags found:`, invalidETags);
      throw new Error(`Invalid ETags in parts: ${invalidETags.map(p => p.PartNumber).join(', ')}`);
    }

    console.log(`✅ [VALIDATION] All ${sortedParts.length} parts validated successfully`);
    console.log(`🔍 [DEBUG] Parts to combine:`);
    sortedParts.forEach((part, index) => {
      console.log(`  Part ${index + 1}: PartNumber=${part.PartNumber}, ETag=${part.ETag}`);
    });

    console.log(`🔍 [DEBUG] Calling completeMultipartUpload with validated parts...`);

    // Add retry logic for completion step
    let result;
    let completionAttempts = 0;
    const maxCompletionAttempts = 3;

    while (completionAttempts < maxCompletionAttempts) {
      try {
        completionAttempts++;
        console.log(`🔄 [RETRY] Completion attempt ${completionAttempts}/${maxCompletionAttempts}`);

        result = await completeMultipartUpload(
          S3_CONFIG.BUCKET_NAME,
          sessionData.key,
          sessionData.uploadId,
          sortedParts // Use sorted and validated parts
        );

        console.log(`✅ [COMPLETION] Multipart upload completed successfully on attempt ${completionAttempts}`);
        break; // Success, exit retry loop

      } catch (completionError) {
        console.error(`❌ [RETRY] Completion attempt ${completionAttempts} failed:`, completionError);

        if (completionAttempts >= maxCompletionAttempts) {
          console.error(`❌ [COMPLETION] All ${maxCompletionAttempts} completion attempts failed`);
          throw completionError;
        }

        // Wait before retry (exponential backoff)
        const retryDelay = Math.pow(2, completionAttempts - 1) * 1000; // 1s, 2s, 4s
        console.log(`⏳ [RETRY] Waiting ${retryDelay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }

    console.log(`🔍 [DEBUG] completeMultipartUpload result:`, result);

    // Calculate total size from parts (approximate)
    const totalSize = sessionData.parts.length * 5 * 1024 * 1024; // Approximate 5MB per part

    console.log(`✅ Session multipart upload completed successfully!`);
    console.log(`🔗 Final file location: ${result.Location}`);
    console.log(`📝 Final ETag: ${result.ETag}`);
    console.log(`📊 Total parts combined: ${sessionData.parts.length}`);
    console.log(`📏 Estimated total size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);

    // Don't clean up session data immediately - let it be cleaned up manually
    // This prevents race conditions with final chunk uploads
    console.log(`🔒 [DEBUG] Session data preserved for ${sessionId} (manual cleanup required)`);

    const finalResult = {
      success: true,
      sessionId,
      finalKey: sessionData.key,
      location: result.Location,
      etag: result.ETag,
      totalParts: sessionData.parts.length,
      totalSize: totalSize,
      userId: sessionData.userId
    };

    console.log(`🔍 [DEBUG] Final result:`, finalResult);
    return finalResult;
  } catch (error) {
    console.error(`❌ Failed to finish session multipart upload for ${sessionId}:`, error);
    console.error(`🔍 [DEBUG] Error details:`, {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.statusCode
    });
    throw error;
  }
}

/**
 * Uploads a manifest file for the multipart upload recording session
 * @param {string} userId - User identifier
 * @param {string} sessionId - Recording session timestamp
 * @param {Object} multipartResult - Result from finishSessionMultipartUpload
 * @returns {Promise<Object>} Upload result
 */
export async function uploadMultipartManifest(userId, sessionId, multipartResult) {
  const manifest = {
    userId,
    sessionId,
    startTime: sessionId,
    segmentationType: 'multipart-consolidated', // New type for multipart uploads
    targetChunkSize: '5MB+',
    finalFile: {
      key: multipartResult.finalKey,
      location: multipartResult.location,
      etag: multipartResult.etag,
      totalParts: multipartResult.totalParts,
      totalSize: multipartResult.totalSize
    },
    uploadMethod: 'multipart',
    createdAt: new Date().toISOString(),
    completedAt: new Date().toISOString()
  };

  const manifestKey = `recordings/${userId}/${sessionId}/manifest.json`;
  console.log(`📄 Uploading multipart manifest to ${manifestKey}:`, manifest);

  try {
    const params = {
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: manifestKey,
      Body: JSON.stringify(manifest, null, 2),
      ContentType: 'application/json',
    };

    const result = await s3.putObject(params).promise();
    console.log(`✅ Multipart manifest uploaded successfully: ${manifestKey}`);
    return { success: true, key: manifestKey, etag: result.ETag };
  } catch (error) {
    console.error(`❌ Failed to upload multipart manifest:`, error);
    throw error;
  }
}

/**
 * Uploads a manifest file for the size-based recording session (individual chunks)
 * @param {string} userId - User identifier
 * @param {string} sessionId - Recording session timestamp
 * @param {Array} chunks - Array of uploaded chunk metadata
 */
export async function uploadSizeBasedManifest(userId, sessionId, chunks) {
  const manifest = {
    userId,
    sessionId,
    startTime: sessionId,
    totalChunks: chunks.length,
    segmentationType: 'size-based', // Distinguish from time-based
    targetChunkSize: '5MB+',
    chunks: chunks.map((chunk) => ({
      index: chunk.chunkIndex,
      key: chunk.s3Key,
      timestamp: chunk.timestamp,
      size: chunk.size,
      etag: chunk.etag
    })),
    totalSize: chunks.reduce((sum, chunk) => sum + chunk.size, 0),
    uploadMethod: 'individual-chunks',
    createdAt: new Date().toISOString()
  };

  const manifestKey = `recordings/${userId}/${sessionId}/manifest.json`;
  console.log(`📄 Uploading size-based manifest to ${manifestKey}:`, manifest);

  try {
    const params = {
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: manifestKey,
      Body: JSON.stringify(manifest, null, 2),
      ContentType: 'application/json',
    };

    const result = await s3.putObject(params).promise();
    console.log(`✅ Size-based manifest uploaded successfully: ${manifestKey}`);
    return { success: true, key: manifestKey, etag: result.ETag };
  } catch (error) {
    console.error(`❌ Failed to upload size-based manifest:`, error);
    throw error;
  }
}

/**
 * Aborts a session-based multipart upload and cleans up
 * @param {string} sessionId - Recording session timestamp
 * @returns {Promise<boolean>} Success status
 */
export async function abortSessionMultipartUpload(sessionId) {
  try {
    const sessionData = sessionMultipartUploads.get(sessionId);
    if (!sessionData) {
      console.log(`ℹ️ No active multipart upload for session ${sessionId} to abort`);
      return true;
    }

    console.log(`🛑 Aborting session multipart upload for session ${sessionId}`);
    console.log(`📋 Upload ID: ${sessionData.uploadId}`);
    console.log(`🔑 Key: ${sessionData.key}`);

    const params = {
      Bucket: S3_CONFIG.BUCKET_NAME,
      Key: sessionData.key,
      UploadId: sessionData.uploadId,
    };

    await s3.abortMultipartUpload(params).promise();

    // Clean up session data
    sessionMultipartUploads.delete(sessionId);

    console.log(`✅ Session multipart upload aborted successfully for ${sessionId}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to abort session multipart upload for ${sessionId}:`, error);
    return false;
  }
}

/**
 * Lists all active session multipart uploads
 * @returns {Array} Array of active session info
 */
export function getActiveSessionUploads() {
  const activeSessions = [];
  for (const [sessionId, sessionData] of sessionMultipartUploads.entries()) {
    activeSessions.push({
      sessionId,
      uploadId: sessionData.uploadId,
      key: sessionData.key,
      partsCount: sessionData.parts.length,
      userId: sessionData.userId
    });
  }
  return activeSessions;
}

/**
 * Clears all session multipart upload data (for testing)
 * @returns {void}
 */
export function clearAllSessionUploads() {
  sessionMultipartUploads.clear();
}

/**
 * Manually clean up a specific session's multipart upload data
 * @param {string} sessionId - Session identifier
 * @returns {boolean} True if session was found and cleaned up
 */
export function cleanupSessionUpload(sessionId) {
  const sessionData = sessionMultipartUploads.get(sessionId);
  if (sessionData) {
    sessionMultipartUploads.delete(sessionId);
    console.log(`🧹 [CLEANUP] Session data cleaned up for ${sessionId}`);
    return true;
  } else {
    console.log(`ℹ️ [CLEANUP] No session data found for ${sessionId}`);
    return false;
  }
}

/**
 * Diagnostic function to check multipart upload system health
 * @returns {Object} System diagnostic information
 */
export function diagnoseMultipartSystem() {
  const activeSessions = getActiveSessionUploads();
  const diagnosis = {
    timestamp: new Date().toISOString(),
    activeSessionsCount: activeSessions.length,
    activeSessions: activeSessions,
    s3Config: {
      bucket: S3_CONFIG.BUCKET_NAME,
      region: S3_CONFIG.REGION,
      userId: S3_CONFIG.USER_ID
    },
    systemHealth: 'OK'
  };

  console.log(`\n🔍 [DIAGNOSTIC] Multipart Upload System Status`);
  console.log(`===============================================`);
  console.log(`📊 Active Sessions: ${diagnosis.activeSessionsCount}`);
  console.log(`🪣 S3 Bucket: ${diagnosis.s3Config.bucket}`);
  console.log(`🌍 S3 Region: ${diagnosis.s3Config.region}`);
  console.log(`👤 User ID: ${diagnosis.s3Config.userId}`);

  if (activeSessions.length > 0) {
    console.log(`\n📋 Active Session Details:`);
    activeSessions.forEach((session, index) => {
      console.log(`  Session ${index + 1}:`);
      console.log(`    ID: ${session.sessionId}`);
      console.log(`    Upload ID: ${session.uploadId}`);
      console.log(`    Key: ${session.key}`);
      console.log(`    Parts: ${session.partsCount}`);
      console.log(`    User: ${session.userId}`);
    });
  } else {
    console.log(`ℹ️ No active multipart upload sessions`);
  }

  console.log(`===============================================`);

  return diagnosis;
}

/**
 * Retry wrapper for upload operations
 * @param {Function} uploadFn - Function to retry
 * @param {number} maxRetries - Maximum number of retry attempts
 * @param {number} baseDelay - Base delay in milliseconds
 */
export async function retryUpload(uploadFn, maxRetries = 3, baseDelay = 1000) {
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await uploadFn();
    } catch (error) {
      lastError = error;

      if (attempt === maxRetries) {
        console.error(`Upload failed after ${maxRetries + 1} attempts:`, error);
        throw error;
      }

      const delay = baseDelay * Math.pow(2, attempt); // Exponential backoff
      console.warn(`Upload attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error.message);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
