// src/aws/completeMultipart.js
import { s3 } from "./s3Client";

/**
 * Finishes a multipart upload by telling S3 to stitch the parts together.
 *
 * @param {string} bucket    - e.g. "audio-recorder-poc"
 * @param {string} key       - e.g. "recordings/mubarak/…/final-file.m4a"
 * @param {string} uploadId  - the UploadId you got from CreateMultipartUpload
 * @param {Array<{ETag:string,PartNumber:number}>} parts
 */
export async function completeMultipartUpload(bucket, key, uploadId, parts) {
  console.log(`🔗 Completing multipart upload for ${key}`);
  console.log(`📦 Parts to combine: ${parts.length}`);
  
  // Dedupe / sort your parts array by PartNumber
  const unique = Array.from(
    new Map(parts.map(p => [p.PartNumber, p])).values()
  ).sort((a, b) => a.PartNumber - b.PartNumber);

  console.log(`📋 Unique parts after deduplication: ${unique.length}`);
  unique.forEach((part, index) => {
    console.log(`  Part ${index + 1}: PartNumber=${part.PartNumber}, ETag=${part.ETag}`);
  });

  const params = {
    Bucket: bucket,
    Key: key,
    UploadId: uploadId,
    MultipartUpload: { Parts: unique }
  };

  try {
    const result = await s3.completeMultipartUpload(params).promise();
    console.log("✅ Multipart upload completed successfully!");
    console.log(`🔗 Final file location: ${result.Location}`);
    console.log(`📝 ETag: ${result.ETag}`);
    return result;
  } catch (error) {
    console.error("❌ Failed to complete multipart upload:", error);
    throw error;
  }
}
