// src/aws/demo/multipartUploadDemo.js
// Demo script to test multipart upload functionality

import {
  startSessionMultipartUpload,
  uploadSessionChunk,
  finishSessionMultipartUpload,
  uploadMultipartManifest,
  getSessionMultipartState,
  getActiveSessionUploads
} from '../uploadChunk';

/**
 * Demo function to simulate a complete multipart upload workflow
 */
export async function demoMultipartUpload() {
  console.log('🚀 Starting Multipart Upload Demo');
  console.log('=====================================');

  const userId = 'demo-user';
  const sessionId = Date.now().toString();
  const mockChunks = [
    { filePath: '/mock/chunk1.m4a', size: 5242880 }, // 5MB
    { filePath: '/mock/chunk2.m4a', size: 5242880 }, // 5MB
    { filePath: '/mock/chunk3.m4a', size: 3145728 }  // 3MB (final chunk)
  ];

  try {
    // Step 1: Start multipart upload
    console.log('\n📋 Step 1: Starting multipart upload session');
    const uploadId = await startSessionMultipartUpload(userId, sessionId);
    console.log(`✅ Upload started with ID: ${uploadId}`);
    
    // Check initial state
    const initialState = getSessionMultipartState(sessionId);
    console.log('📊 Initial state:', JSON.stringify(initialState, null, 2));

    // Step 2: Upload chunks
    console.log('\n📦 Step 2: Uploading chunks as multipart parts');
    const uploadResults = [];
    
    for (let i = 0; i < mockChunks.length; i++) {
      const chunk = mockChunks[i];
      console.log(`\n  Uploading chunk ${i + 1}/${mockChunks.length}: ${chunk.filePath}`);
      
      try {
        // Note: In real usage, this would read actual file data
        // For demo, we'll simulate the upload
        const result = await uploadSessionChunk(
          chunk.filePath,
          sessionId,
          i,
          (progress) => {
            const percentage = Math.round((progress.loaded / progress.total) * 100);
            if (percentage % 25 === 0) { // Log every 25%
              console.log(`    Progress: ${percentage}%`);
            }
          }
        );
        
        uploadResults.push(result);
        console.log(`  ✅ Chunk ${i} uploaded successfully`);
        console.log(`     Part Number: ${result.partNumber}`);
        console.log(`     ETag: ${result.etag}`);
        console.log(`     Size: ${result.size} bytes`);
        
      } catch (error) {
        console.error(`  ❌ Failed to upload chunk ${i}:`, error.message);
        throw error;
      }
    }

    // Check state after uploads
    const midState = getSessionMultipartState(sessionId);
    console.log('\n📊 State after uploads:', JSON.stringify(midState, null, 2));

    // Step 3: Complete multipart upload
    console.log('\n🏁 Step 3: Completing multipart upload');
    const completionResult = await finishSessionMultipartUpload(sessionId);
    console.log('✅ Multipart upload completed successfully!');
    console.log('📁 Final file details:');
    console.log(`   Location: ${completionResult.location}`);
    console.log(`   Key: ${completionResult.finalKey}`);
    console.log(`   ETag: ${completionResult.etag}`);
    console.log(`   Total Parts: ${completionResult.totalParts}`);
    console.log(`   Total Size: ${(completionResult.totalSize / 1024 / 1024).toFixed(2)} MB`);

    // Step 4: Upload manifest
    console.log('\n📄 Step 4: Uploading manifest');
    const manifestResult = await uploadMultipartManifest(userId, sessionId, completionResult);
    console.log('✅ Manifest uploaded successfully!');
    console.log(`   Manifest Key: ${manifestResult.key}`);
    console.log(`   Manifest ETag: ${manifestResult.etag}`);

    // Final state check
    const finalState = getSessionMultipartState(sessionId);
    console.log('\n📊 Final state (should be null):', finalState);

    const activeSessions = getActiveSessionUploads();
    console.log('📊 Active sessions (should be empty):', activeSessions);

    console.log('\n🎉 Demo completed successfully!');
    console.log('=====================================');

    return {
      success: true,
      sessionId,
      uploadResults,
      completionResult,
      manifestResult
    };

  } catch (error) {
    console.error('\n❌ Demo failed:', error);
    console.log('=====================================');
    
    // Try to clean up on failure
    try {
      const { abortSessionMultipartUpload } = await import('../uploadChunk');
      await abortSessionMultipartUpload(sessionId);
      console.log('🧹 Cleaned up failed upload');
    } catch (cleanupError) {
      console.error('❌ Failed to clean up:', cleanupError);
    }
    
    throw error;
  }
}

/**
 * Demo function to show session state inspection
 */
export function demoStateInspection() {
  console.log('\n🔍 Session State Inspection Demo');
  console.log('=================================');

  const activeSessions = getActiveSessionUploads();
  console.log(`📊 Active sessions: ${activeSessions.length}`);
  
  if (activeSessions.length > 0) {
    activeSessions.forEach((session, index) => {
      console.log(`\n  Session ${index + 1}:`);
      console.log(`    ID: ${session.sessionId}`);
      console.log(`    Upload ID: ${session.uploadId}`);
      console.log(`    Key: ${session.key}`);
      console.log(`    Parts: ${session.partsCount}`);
      console.log(`    User: ${session.userId}`);
      
      const detailedState = getSessionMultipartState(session.sessionId);
      if (detailedState) {
        console.log(`    Parts Details:`, detailedState.parts);
      }
    });
  } else {
    console.log('  No active sessions');
  }
  
  console.log('=================================');
}

/**
 * Demo function to simulate error scenarios
 */
export async function demoErrorHandling() {
  console.log('\n⚠️  Error Handling Demo');
  console.log('========================');

  const userId = 'demo-user';
  const sessionId = `error-test-${Date.now()}`;

  try {
    // Test 1: Upload chunk without starting session
    console.log('\n🧪 Test 1: Upload chunk without session');
    try {
      await uploadSessionChunk('/mock/chunk.m4a', 'non-existent-session', 0);
      console.log('❌ Should have failed');
    } catch (error) {
      console.log('✅ Correctly failed:', error.message);
    }

    // Test 2: Complete upload without parts
    console.log('\n🧪 Test 2: Complete upload without parts');
    await startSessionMultipartUpload(userId, sessionId);
    try {
      await finishSessionMultipartUpload(sessionId);
      console.log('❌ Should have failed');
    } catch (error) {
      console.log('✅ Correctly failed:', error.message);
    }

    // Test 3: Abort upload
    console.log('\n🧪 Test 3: Abort upload');
    const { abortSessionMultipartUpload } = await import('../uploadChunk');
    const aborted = await abortSessionMultipartUpload(sessionId);
    console.log(`✅ Upload aborted: ${aborted}`);

    console.log('\n✅ Error handling tests completed');
    console.log('========================');

  } catch (error) {
    console.error('❌ Error handling demo failed:', error);
    console.log('========================');
  }
}

// Export demo runner
export async function runAllDemos() {
  console.log('🎬 Running All Multipart Upload Demos');
  console.log('======================================');

  try {
    await demoMultipartUpload();
    demoStateInspection();
    await demoErrorHandling();
    
    console.log('\n🎉 All demos completed successfully!');
  } catch (error) {
    console.error('\n❌ Demo suite failed:', error);
  }
}

// If running directly
if (require.main === module) {
  runAllDemos();
}
