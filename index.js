/**
 * @format
 */

// AWS SDK polyfills - must be imported first
import 'react-native-url-polyfill/auto';       // URL, fetch polyfill
import 'react-native-get-random-values';       // crypto.getRandomValues()
import { Buffer } from 'buffer';
import process from 'process';
import { EventEmitter } from 'events';

// Set up global polyfills
global.Buffer = Buffer;
global.process = process;
global.EventEmitter = EventEmitter;

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';

AppRegistry.registerComponent(appName, () => App);
