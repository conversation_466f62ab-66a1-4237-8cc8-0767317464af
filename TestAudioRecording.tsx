import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import { NativeModules, DeviceEventEmitter } from 'react-native';

const { AudioRecorderModule } = NativeModules;

interface RecordingInfo {
  isRecording: boolean;
  totalDuration?: number;
  segmentDuration?: number;
  currentSegment?: number;
  currentFilePath?: string;
  segmentDurationMinutes?: number;
}

interface RecordingFile {
  path: string;
  name: string;
  size: number;
  lastModified: number;
}

interface PlaybackStatus {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  progress: number;
  filePath: string;
  formattedCurrentTime: string;
  formattedDuration: string;
}

const TestAudioRecording: React.FC = () => {
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recordingInfo, setRecordingInfo] = useState<RecordingInfo | null>(null);
  const [recordingFiles, setRecordingFiles] = useState<RecordingFile[]>([]);
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [playbackStatus, setPlaybackStatus] = useState<PlaybackStatus | null>(null);
  const [currentlyPlayingFile, setCurrentlyPlayingFile] = useState<string | null>(null);

  useEffect(() => {
    // Check initial permission status
    checkPermissions();

    // Listen for recording status changes
    const statusListener = DeviceEventEmitter.addListener(
      'recordingStatusChanged',
      (data) => {
        console.log('Recording status changed:', data);
        setIsRecording(data.isRecording);
      }
    );

    return () => {
      statusListener.remove();
    };
  }, []);

  // Playback status update effect
  useEffect(() => {
    let playbackInterval: NodeJS.Timeout;

    if (currentlyPlayingFile) {
      playbackInterval = setInterval(async () => {
        try {
          const status = await AudioRecorderModule.getPlaybackStatus();
          setPlaybackStatus(status);

          if (!status.isPlaying && status.currentTime === 0) {
            // Playback finished
            setCurrentlyPlayingFile(null);
            setPlaybackStatus(null);
          }
        } catch (error) {
          console.error('Failed to get playback status:', error);
        }
      }, 100);
    } else {
      setPlaybackStatus(null);
    }

    return () => {
      if (playbackInterval) {
        clearInterval(playbackInterval);
      }
    };
  }, [currentlyPlayingFile]);

  const checkPermissions = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          {
            title: 'Audio Recording Permission',
            message: 'This app needs access to your microphone to record audio.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );
        
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setHasPermission(true);
          console.log('Microphone permission granted');
        } else {
          setHasPermission(false);
          console.log('Microphone permission denied');
        }
      } else {
        // For iOS, use the existing permission method
        try {
          const result = await AudioRecorderModule.requestMicrophonePermission();
          setHasPermission(result.granted);
        } catch (error) {
          console.log('Permission error:', error);
          setHasPermission(false);
        }
      }
    } catch (error) {
      console.error('Error checking permissions:', error);
      setHasPermission(false);
    }
  };

  const startRecording = async () => {
    try {
      if (!hasPermission) {
        Alert.alert('Permission Required', 'Please grant microphone permission first.');
        return;
      }

      const result = await AudioRecorderModule.startRecording(5); // 5-minute segments
      console.log('Recording started:', result);
      setIsRecording(true);
      
      Alert.alert(
        'Recording Started',
        'Audio recording has started. You can now background the app to test background recording.',
        [{ text: 'OK' }]
      );
    } catch (error: any) {
      console.error('Error starting recording:', error);
      Alert.alert('Error', `Failed to start recording: ${error.message}`);
    }
  };

  const stopRecording = async () => {
    try {
      const result = await AudioRecorderModule.stopRecording();
      console.log('Recording stopped:', result);
      setIsRecording(false);
      
      Alert.alert('Recording Stopped', 'Audio recording has been stopped.');
      
      // Refresh the file list
      await loadRecordingFiles();
    } catch (error: any) {
      console.error('Error stopping recording:', error);
      Alert.alert('Error', `Failed to stop recording: ${error.message}`);
    }
  };

  const getRecordingInfo = async () => {
    try {
      const info = await AudioRecorderModule.getCurrentRecordingInfo();
      console.log('Recording info:', info);
      setRecordingInfo(info);
    } catch (error) {
      console.error('Error getting recording info:', error);
    }
  };

  const loadRecordingFiles = async () => {
    try {
      const files = await AudioRecorderModule.getAllRecordingFiles();
      console.log('Recording files:', files);
      setRecordingFiles(files);
    } catch (error) {
      console.error('Error loading recording files:', error);
    }
  };

  const deleteFile = async (filePath: string) => {
    try {
      const result = await AudioRecorderModule.deleteRecordingFile(filePath);
      if (result.success) {
        Alert.alert('File Deleted', 'Recording file has been deleted.');
        await loadRecordingFiles(); // Refresh the list
      } else {
        Alert.alert('Error', 'Failed to delete the file.');
      }
    } catch (error: any) {
      console.error('Error deleting file:', error);
      Alert.alert('Error', `Failed to delete file: ${error.message}`);
    }
  };

  const playRecording = async (filePath: string) => {
    try {
      console.log('Attempting to play file:', filePath);

      // Stop any current playback
      if (currentlyPlayingFile) {
        console.log('Stopping current playback first');
        await AudioRecorderModule.stopPlayback();
      }

      const result = await AudioRecorderModule.playRecording(filePath);
      console.log('Playback started result:', result);

      if (result.success) {
        setCurrentlyPlayingFile(filePath);
        Alert.alert('Playback Started', `Playing: ${filePath.split('/').pop()}\n\nIf you don't hear sound:\n1. Check device volume\n2. Check if device is muted\n3. Try using headphones`);
      } else {
        Alert.alert('Playback Failed', 'Failed to start playback. Check if the file exists and is valid.');
      }
    } catch (error: any) {
      console.error('Play recording error:', error);
      Alert.alert('Error', `Failed to play recording: ${error.message}\n\nTroubleshooting:\n1. Check device volume\n2. Ensure file exists\n3. Try restarting the app`);
    }
  };

  const stopPlayback = async () => {
    try {
      await AudioRecorderModule.stopPlayback();
      setCurrentlyPlayingFile(null);
      setPlaybackStatus(null);

      Alert.alert('Playback Stopped', 'Audio playback has been stopped.');
    } catch (error: any) {
      console.error('Stop playback error:', error);
      Alert.alert('Error', `Failed to stop playback: ${error.message}`);
    }
  };

  const formatDuration = (ms: number): string => {
    const seconds = Math.floor((ms / 1000) % 60);
    const minutes = Math.floor((ms / (1000 * 60)) % 60);
    const hours = Math.floor(ms / (1000 * 60 * 60));
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Android Audio Recording Test</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Permission Status</Text>
        <Text style={[styles.status, { color: hasPermission ? 'green' : 'red' }]}>
          {hasPermission ? 'Microphone permission granted' : 'Microphone permission required'}
        </Text>
        {!hasPermission && (
          <TouchableOpacity style={styles.button} onPress={checkPermissions}>
            <Text style={styles.buttonText}>Request Permission</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recording Controls</Text>
        <Text style={[styles.status, { color: isRecording ? 'green' : 'gray' }]}>
          {isRecording ? 'Recording in progress...' : 'Not recording'}
        </Text>
        
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.button, { backgroundColor: isRecording ? '#ccc' : '#007AFF' }]}
            onPress={startRecording}
            disabled={isRecording || !hasPermission}
          >
            <Text style={styles.buttonText}>Start Recording</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, { backgroundColor: !isRecording ? '#ccc' : '#FF3B30' }]}
            onPress={stopRecording}
            disabled={!isRecording}
          >
            <Text style={styles.buttonText}>Stop Recording</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recording Info</Text>
        <TouchableOpacity style={styles.button} onPress={getRecordingInfo}>
          <Text style={styles.buttonText}>Get Recording Info</Text>
        </TouchableOpacity>
        
        {recordingInfo && (
          <View style={styles.infoContainer}>
            <Text>Is Recording: {recordingInfo.isRecording ? 'Yes' : 'No'}</Text>
            {recordingInfo.isRecording && (
              <>
                <Text>Total Duration: {formatDuration(recordingInfo.totalDuration || 0)}</Text>
                <Text>Current Segment: {recordingInfo.currentSegment}</Text>
                <Text>Segment Duration: {recordingInfo.segmentDurationMinutes} minutes</Text>
                <Text>Current File: {recordingInfo.currentFilePath}</Text>
              </>
            )}
          </View>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recording Files</Text>
        <TouchableOpacity style={styles.button} onPress={loadRecordingFiles}>
          <Text style={styles.buttonText}>Load Recording Files</Text>
        </TouchableOpacity>
        
        {recordingFiles.map((file, index) => (
          <View key={index} style={styles.fileItem}>
            <Text style={styles.fileName}>{file.name}</Text>
            <Text style={styles.fileInfo}>Size: {formatFileSize(file.size)}</Text>
            <Text style={styles.fileInfo}>
              Modified: {new Date(file.lastModified).toLocaleString()}
            </Text>

            {/* Playback Controls */}
            <View style={styles.playbackControls}>
              {currentlyPlayingFile === file.path ? (
                <>
                  {playbackStatus && (
                    <View style={styles.playbackInfo}>
                      <Text style={styles.playbackTime}>
                        {playbackStatus.formattedCurrentTime} / {playbackStatus.formattedDuration}
                      </Text>
                      <View style={styles.progressBar}>
                        <View
                          style={[
                            styles.progressFill,
                            {
                              width: `${(playbackStatus.progress * 100)}%`,
                            }
                          ]}
                        />
                      </View>
                    </View>
                  )}
                  <TouchableOpacity
                    style={[styles.button, { backgroundColor: '#FF3B30', marginTop: 5 }]}
                    onPress={stopPlayback}
                  >
                    <Text style={styles.buttonText}>⏹️ Stop Playback</Text>
                  </TouchableOpacity>
                </>
              ) : (
                <TouchableOpacity
                  style={[styles.button, { backgroundColor: '#27ae60', marginTop: 5 }]}
                  onPress={() => playRecording(file.path)}
                >
                  <Text style={styles.buttonText}>▶️ Play</Text>
                </TouchableOpacity>
              )}
            </View>

            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#FF3B30', marginTop: 5 }]}
              onPress={() => deleteFile(file.path)}
            >
              <Text style={styles.buttonText}>🗑️ Delete</Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test Instructions</Text>
        <Text style={styles.instructions}>
          1. Grant microphone permission{'\n'}
          2. Start recording{'\n'}
          3. Background the app (press home button){'\n'}
          4. Wait a few minutes{'\n'}
          5. Return to the app{'\n'}
          6. Check recording info to verify it continued{'\n'}
          7. Stop recording and check files{'\n'}
          {'\n'}
          For phone call testing:{'\n'}
          - Start recording{'\n'}
          - Make or receive a phone call{'\n'}
          - End the call{'\n'}
          - Verify recording resumed
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  section: {
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 15,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  status: {
    fontSize: 16,
    marginBottom: 10,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 6,
    marginTop: 10,
  },
  fileItem: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 6,
    marginTop: 10,
  },
  fileName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  fileInfo: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  instructions: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
  playbackControls: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  playbackInfo: {
    marginBottom: 10,
  },
  playbackTime: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 5,
    color: '#3498db',
  },
  progressBar: {
    height: 4,
    backgroundColor: '#ddd',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3498db',
    borderRadius: 2,
  },
});

export default TestAudioRecording;
