App.tsx:51 Available NativeModules: Array(0)
App.tsx:52 AudioRecorderModule: Object
console.js:654 Running "AlwaysOnRecorder" with {"rootTag":1,"initialProps":null,"fabric":true}
s3Client.js:45 ✅ S3 Client initialized
s3Client.js:46 Region: ap-south-1
s3Client.js:47 Bucket: audio-recorder-poc
s3Client.js:48 Auth: Hardcoded (testing only)
s3Client.js:55 🔐 Testing AWS credentials...
App.tsx:941 📊 Local storage usage: 0.00 MB (0 files)
App.tsx:433 📱 Setting up iOS event listener for onNewAudioChunk
App.tsx:434 📱 Current session ID when setting up listener: null
App.tsx:438 📱 handleNewAudioChunk function: function
App.tsx:468 📱 Event listener subscriptions created: Object
s3Client.js:61 ✅ AWS credentials are valid!
s3Client.js:62 📁 Bucket accessible: audio-recorder-poc
App.tsx:451 📱 Raw onNewAudioChunk event received from iOS: Object
App.tsx:177 🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
App.tsx:178 ⏰ Event received at: 2025-07-28T12:48:52.293Z
App.tsx:181 📦 Chunk Info: {
  "chunkIndex": 999,
  "filePath": "/test/manual/path.m4a",
  "fileSize": 5242880,
  "timestamp": 1753706932292
}
App.tsx:182 📦 Current Session ID: null
App.tsx:183 📦 Is Recording: false
App.tsx:184 📦 Use Multipart Upload: true
App.tsx:185 🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 999
App.tsx:188 🔍 [FINAL CHUNK] Is final chunk: false
App.tsx:419 🧪 Testing React Native event handler directly
App.tsx:177 🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
App.tsx:178 ⏰ Event received at: 2025-07-28T12:48:54.393Z
App.tsx:181 📦 Chunk Info: {
  "filePath": "/test/direct/path.m4a",
  "chunkIndex": 888,
  "fileSize": 5242880,
  "timestamp": 1753706934393
}
App.tsx:182 📦 Current Session ID: null
App.tsx:183 📦 Is Recording: false
App.tsx:184 📦 Use Multipart Upload: true
App.tsx:185 🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 888
App.tsx:188 🔍 [FINAL CHUNK] Is final chunk: false
App.tsx:617 🚀 Starting size-based recording session: 1753706945438
App.tsx:618 📏 Chunks will be uploaded automatically when they reach 5MB
App.tsx:622 🎬 Starting recording with size-based segmentation (5MB chunks), platform: ios
App.tsx:625 🔍 AudioRecorderModule methods: Array(5)
App.tsx:629 🔍 startRecording method type: function
App.tsx:633 🔍 CRITICAL DEBUG - Starting recording with size-based segmentation (always enabled)
App.tsx:474 📱 Removing iOS event listeners
App.tsx:433 📱 Setting up iOS event listener for onNewAudioChunk
App.tsx:434 📱 Current session ID when setting up listener: 1753706945438
App.tsx:438 📱 handleNewAudioChunk function: function
App.tsx:468 📱 Event listener subscriptions created: Object
App.tsx:460 🧪 Test event received from iOS: Object
App.tsx:642 Recording started: Object
App.tsx:643 S3 Session ID: 1753706945438
App.tsx:474 📱 Removing iOS event listeners
App.tsx:433 📱 Setting up iOS event listener for onNewAudioChunk
App.tsx:434 📱 Current session ID when setting up listener: 1753706945438
App.tsx:438 📱 handleNewAudioChunk function: function
App.tsx:468 📱 Event listener subscriptions created: Object
Welcome to React Native DevTools
Debugger integration: iOS Bridgeless (RCTHost)
console.js:654 Failed to open debugger. Please check that the dev server is running and reload the app.
anonymous @ console.js:654
overrideMethod @ backend.js:17416
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:96
logIfNoNativeHook @ RCTLog.js:34
Show 6 more frames
Show less
App.tsx:451 📱 Raw onNewAudioChunk event received from iOS: {hasMovAtom: true, chunkIndex: 0, fileSize: 5377446, timestamp: 1753707281697, filePath: '/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_181905_001.m4a'}
App.tsx:177 🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
App.tsx:178 ⏰ Event received at: 2025-07-28T12:54:41.698Z
App.tsx:181 📦 Chunk Info: {
  "hasMovAtom": true,
  "chunkIndex": 0,
  "fileSize": 5377446,
  "timestamp": 1753707281697,
  "filePath": "/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_181905_001.m4a"
}
App.tsx:182 📦 Current Session ID: 1753706945438
App.tsx:183 📦 Is Recording: true
App.tsx:184 📦 Use Multipart Upload: true
App.tsx:185 🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 0
App.tsx:188 🔍 [FINAL CHUNK] Is final chunk: false
App.tsx:214 ⏱️ [TIMING] Upload started 1ms after event received
App.tsx:223 🚀 [IMMEDIATE UPLOAD] Starting session multipart upload for first chunk (index 0)
App.tsx:226 🔍 [VERIFICATION] This should be the very first chunk of the recording session
uploadChunk.js:57 
� [DEBUG] Starting startSessionMultipartUpload
uploadChunk.js:58 �🚀 Starting session multipart upload for: recordings/mubarak/1753706945438/recording-1753706945438.m4a
uploadChunk.js:59 📋 Session ID: 1753706945438
uploadChunk.js:60 👤 User ID: mubarak
uploadChunk.js:61 🪣 Bucket: audio-recorder-poc
uploadChunk.js:70 🔍 [DEBUG] CreateMultipartUpload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753706945438/recording-1753706945438.m4a', ContentType: 'audio/m4a'}
uploadChunk.js:71 🔍 [DEBUG] Calling s3.createMultipartUpload...
uploadChunk.js:74 🔍 [DEBUG] s3.createMultipartUpload result: {ServerSideEncryption: 'AES256', Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753706945438/recording-1753706945438.m4a', UploadId: '_mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z', $response: {…}}
uploadChunk.js:85 🔍 [DEBUG] Session data stored: {uploadId: '_mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z', key: 'recordings/mubarak/1753706945438/recording-1753706945438.m4a', parts: Array(0), userId: 'mubarak'}
uploadChunk.js:86 🔍 [DEBUG] Total active sessions: 1
uploadChunk.js:88 ✅ Session multipart upload started successfully!
uploadChunk.js:89   UploadId: _mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z
uploadChunk.js:90   Key: recordings/mubarak/1753706945438/recording-1753706945438.m4a
uploadChunk.js:91   Bucket: audio-recorder-poc
uploadChunk.js:92   Session: 1753706945438
App.tsx:234 ⏱️ [TIMING] Multipart session started in 169ms
App.tsx:243 🔍 [IMMEDIATE UPLOAD] Uploading chunk 0 as part 1
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753706945438, chunk: 0
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_181905_001.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: YES
uploadChunk.js:310 
🔄 Uploading session chunk 0 (part 1) for session 1753706945438
uploadChunk.js:311 📋 Upload ID: _mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z
uploadChunk.js:312 🔑 Key: recordings/mubarak/1753706945438/recording-1753706945438.m4a
uploadChunk.js:313 🪣 Bucket: audio-recorder-poc
uploadChunk.js:316 🔍 [DEBUG] Reading file: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_181905_001.m4a
App.tsx:474 📱 Removing iOS event listeners
App.tsx:433 📱 Setting up iOS event listener for onNewAudioChunk
App.tsx:434 📱 Current session ID when setting up listener: 1753706945438
App.tsx:438 📱 handleNewAudioChunk function: function
App.tsx:468 📱 Event listener subscriptions created: {chunkSubscription: {…}, testSubscription: {…}}
uploadChunk.js:320 📁 File read successfully. Size: 5377446 bytes (5.13 MB)
uploadChunk.js:321 🔍 [DEBUG] File buffer created, length: 5377446
uploadChunk.js:337 🔍 [DEBUG] Upload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753706945438/recording-1753706945438.m4a', PartNumber: 1, UploadId: '_mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z', BodySize: 5377446}
uploadChunk.js:345 🔍 [DEBUG] Calling s3.uploadPart...
uploadChunk.js:347 🔍 [DEBUG] s3.uploadPart result: {ServerSideEncryption: 'AES256', ETag: '"2925dafe8c779b3b6dc1acd10efbb1d0"', $response: {…}}
uploadChunk.js:349 📡 Session chunk 0 upload response: ETag="2925dafe8c779b3b6dc1acd10efbb1d0"
uploadChunk.js:355 ✅ SUCCESS: Uploaded session chunk 0 as part 1
uploadChunk.js:356 📦 Total parts uploaded for session: 1
uploadChunk.js:357 🔍 [DEBUG] Current parts: [{…}]
uploadChunk.js:373 🔍 [DEBUG] Upload result: {success: true, chunkIndex: 0, partNumber: 1, etag: '"2925dafe8c779b3b6dc1acd10efbb1d0"', size: 5377446, sessionId: '1753706945438'}
App.tsx:267 ⏱️ [TIMING] Chunk 0 uploaded in 5145ms
App.tsx:299 ✅ Successfully uploaded multipart chunk 0 (part 1)
App.tsx:352 � Local file preserved: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_181905_001.m4a
App.tsx:353 💾 File size: 5.13 MB
App.tsx:451 📱 Raw onNewAudioChunk event received from iOS: {hasMovAtom: true, chunkIndex: 1, filePath: '/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_182441_002.m4a', fileSize: 5355995, timestamp: 1753707616695}
App.tsx:177 🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
App.tsx:178 ⏰ Event received at: 2025-07-28T13:00:16.696Z
App.tsx:181 📦 Chunk Info: {
  "hasMovAtom": true,
  "chunkIndex": 1,
  "filePath": "/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_182441_002.m4a",
  "fileSize": 5355995,
  "timestamp": 1753707616695
}
App.tsx:182 📦 Current Session ID: 1753706945438
App.tsx:183 📦 Is Recording: true
App.tsx:184 📦 Use Multipart Upload: true
App.tsx:185 🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 1
App.tsx:188 🔍 [FINAL CHUNK] Is final chunk: false
App.tsx:214 ⏱️ [TIMING] Upload started 1ms after event received
App.tsx:243 🔍 [IMMEDIATE UPLOAD] Uploading chunk 1 as part 2
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753706945438, chunk: 1
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_182441_002.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: YES
uploadChunk.js:310 
🔄 Uploading session chunk 1 (part 2) for session 1753706945438
uploadChunk.js:311 📋 Upload ID: _mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z
uploadChunk.js:312 🔑 Key: recordings/mubarak/1753706945438/recording-1753706945438.m4a
uploadChunk.js:313 🪣 Bucket: audio-recorder-poc
uploadChunk.js:316 🔍 [DEBUG] Reading file: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_182441_002.m4a
uploadChunk.js:320 📁 File read successfully. Size: 5355995 bytes (5.11 MB)
uploadChunk.js:321 🔍 [DEBUG] File buffer created, length: 5355995
uploadChunk.js:337 🔍 [DEBUG] Upload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753706945438/recording-1753706945438.m4a', PartNumber: 2, UploadId: '_mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z', BodySize: 5355995}BodySize: 5355995Bucket: "audio-recorder-poc"Key: "recordings/mubarak/1753706945438/recording-1753706945438.m4a"PartNumber: 2UploadId: "_mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z"[[Prototype]]: Object
uploadChunk.js:345 🔍 [DEBUG] Calling s3.uploadPart...
uploadChunk.js:347 🔍 [DEBUG] s3.uploadPart result: {ServerSideEncryption: 'AES256', ETag: '"394b0f6a7c4f393354bb861458652054"', $response: {…}}
uploadChunk.js:349 📡 Session chunk 1 upload response: ETag="394b0f6a7c4f393354bb861458652054"
uploadChunk.js:355 ✅ SUCCESS: Uploaded session chunk 1 as part 2
uploadChunk.js:356 📦 Total parts uploaded for session: 2
uploadChunk.js:357 🔍 [DEBUG] Current parts: (2) [{…}, {…}]0: {ETag: '"2925dafe8c779b3b6dc1acd10efbb1d0"', PartNumber: 1}1: {ETag: '"394b0f6a7c4f393354bb861458652054"', PartNumber: 2}length: 2[[Prototype]]: Array(0)
uploadChunk.js:373 🔍 [DEBUG] Upload result: {success: true, chunkIndex: 1, partNumber: 2, etag: '"394b0f6a7c4f393354bb861458652054"', size: 5355995, sessionId: '1753706945438'}chunkIndex: 1etag: "\"394b0f6a7c4f393354bb861458652054\""partNumber: 2sessionId: "1753706945438"size: 5355995success: true[[Prototype]]: Object
App.tsx:267 ⏱️ [TIMING] Chunk 1 uploaded in 4644ms
App.tsx:299 ✅ Successfully uploaded multipart chunk 1 (part 2)
App.tsx:352 � Local file preserved: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_182441_002.m4a
App.tsx:353 💾 File size: 5.11 MB
App.tsx:665 Recording stopped: {success: true}
App.tsx:669 ⏳ [RACE CONDITION FIX] Waiting for pending uploads to complete...
App.tsx:672 🔍 [RACE CONDITION FIX] Final chunk received: false
App.tsx:451 📱 Raw onNewAudioChunk event received from iOS: {filePath: '/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_183016_003.m4a', hasMovAtom: true, isFinalChunk: true, fileSize: 3723848, timestamp: 1753707850942, chunkIndex: 2}
App.tsx:177 🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
App.tsx:178 ⏰ Event received at: 2025-07-28T13:04:10.945Z
App.tsx:181 📦 Chunk Info: {
  "filePath": "/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_183016_003.m4a",
  "hasMovAtom": true,
  "isFinalChunk": true,
  "fileSize": 3723848,
  "timestamp": 1753707850942,
  "chunkIndex": 2
}
App.tsx:182 📦 Current Session ID: 1753706945438
App.tsx:183 📦 Is Recording: true
App.tsx:184 📦 Use Multipart Upload: true
App.tsx:185 🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 2
App.tsx:188 🔍 [FINAL CHUNK] Is final chunk: true
App.tsx:193 📦 [FINAL CHUNK] Processing final partial chunk (3.55 MB)
App.tsx:214 ⏱️ [TIMING] Upload started 2ms after event received
App.tsx:243 🔍 [IMMEDIATE UPLOAD] Uploading chunk 2 as part 3
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753706945438, chunk: 2
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_183016_003.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: YES
uploadChunk.js:310 
🔄 Uploading session chunk 2 (part 3) for session 1753706945438
uploadChunk.js:311 📋 Upload ID: _mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z
uploadChunk.js:312 🔑 Key: recordings/mubarak/1753706945438/recording-1753706945438.m4a
uploadChunk.js:313 🪣 Bucket: audio-recorder-poc
uploadChunk.js:316 🔍 [DEBUG] Reading file: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_183016_003.m4a
App.tsx:474 📱 Removing iOS event listeners
App.tsx:433 📱 Setting up iOS event listener for onNewAudioChunk
App.tsx:434 📱 Current session ID when setting up listener: 1753706945438
App.tsx:438 📱 handleNewAudioChunk function: function
App.tsx:468 📱 Event listener subscriptions created: {chunkSubscription: {…}, testSubscription: {…}}
uploadChunk.js:320 📁 File read successfully. Size: 3723848 bytes (3.55 MB)
uploadChunk.js:321 🔍 [DEBUG] File buffer created, length: 3723848
uploadChunk.js:337 🔍 [DEBUG] Upload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753706945438/recording-1753706945438.m4a', PartNumber: 3, UploadId: '_mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z', BodySize: 3723848}
uploadChunk.js:345 🔍 [DEBUG] Calling s3.uploadPart...
App.tsx:710 ⏳ [RACE CONDITION FIX] Waiting for 2 remaining uploads to finish...
uploadChunk.js:347 🔍 [DEBUG] s3.uploadPart result: {ServerSideEncryption: 'AES256', ETag: '"0d0a10693d4c1fd24e2609697b15500e"', $response: {…}}
uploadChunk.js:349 📡 Session chunk 2 upload response: ETag="0d0a10693d4c1fd24e2609697b15500e"
uploadChunk.js:355 ✅ SUCCESS: Uploaded session chunk 2 as part 3
uploadChunk.js:356 📦 Total parts uploaded for session: 3
uploadChunk.js:357 🔍 [DEBUG] Current parts: (3) [{…}, {…}, {…}]
uploadChunk.js:373 🔍 [DEBUG] Upload result: {success: true, chunkIndex: 2, partNumber: 3, etag: '"0d0a10693d4c1fd24e2609697b15500e"', size: 3723848, sessionId: '1753706945438'}
App.tsx:267 ⏱️ [TIMING] Chunk 2 uploaded in 3564ms
App.tsx:299 ✅ Successfully uploaded multipart chunk 2 (part 3)
App.tsx:352 � Local file preserved: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/55EA57ED-405F-41A5-A742-EF85D4E6A40A/Documents/recording_20250728_183016_003.m4a
App.tsx:353 💾 File size: 3.55 MB
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:733 
🔍 [DEBUG] Processing upload completion for session: 1753706945438
App.tsx:736 🔍 [DEBUG] useMultipartUpload: true
App.tsx:737 🔍 [DEBUG] multipartUploadStarted: true
App.tsx:740 🔍 [DEBUG] multipartUploadedChunks.length: 2
App.tsx:743 🔍 [DEBUG] sizeBasedUploadedChunks.length: 0
App.tsx:754 🏁 Completing session multipart upload...
App.tsx:755 📦 Total multipart chunks to combine: 2
App.tsx:758 🔍 [DEBUG] Multipart chunks details: (2) [{…}, {…}]
uploadChunk.js:485 
🔍 [DEBUG] Starting finishSessionMultipartUpload for session: 1753706945438
uploadChunk.js:488 🔍 [DEBUG] Session data found: YES
uploadChunk.js:496 🔍 [DEBUG] Session data details: {uploadId: '_mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z', key: 'recordings/mubarak/1753706945438/recording-1753706945438.m4a', partsCount: 3, userId: 'mubarak'}
uploadChunk.js:508 🏁 Finishing session multipart upload for session 1753706945438
uploadChunk.js:509 📦 Total parts to combine: 3
uploadChunk.js:510 🔑 Final file key: recordings/mubarak/1753706945438/recording-1753706945438.m4a
uploadChunk.js:511 🆔 Upload ID: _mIF4832xGNp8q8pkRTrTzBD.xbSE90wEP848x9JZQv4XKJCFD0urjWfnwBhWeO59mHhHhai1fa7hPSTdUaOp2M1BvyA3McSnsm.SK4xQOZqZf1FtXVXCIUma4Q96D3z
uploadChunk.js:512 🪣 Bucket: audio-recorder-poc
uploadChunk.js:515 🔍 [VALIDATION] Validating parts before completion...
uploadChunk.js:546 ✅ [VALIDATION] All 3 parts validated successfully
uploadChunk.js:547 🔍 [DEBUG] Parts to combine:
uploadChunk.js:549   Part 1: PartNumber=1, ETag="2925dafe8c779b3b6dc1acd10efbb1d0"
uploadChunk.js:549   Part 2: PartNumber=2, ETag="394b0f6a7c4f393354bb861458652054"
uploadChunk.js:549   Part 3: PartNumber=3, ETag="0d0a10693d4c1fd24e2609697b15500e"
uploadChunk.js:552 🔍 [DEBUG] Calling completeMultipartUpload with validated parts...
uploadChunk.js:562 🔄 [RETRY] Completion attempt 1/3
completeMultipart.js:13 🔗 Completing multipart upload for recordings/mubarak/1753706945438/recording-1753706945438.m4a
completeMultipart.js:14 📦 Parts to combine: 3
completeMultipart.js:21 📋 Unique parts after deduplication: 3
completeMultipart.js:23   Part 1: PartNumber=1, ETag="2925dafe8c779b3b6dc1acd10efbb1d0"
completeMultipart.js:23   Part 2: PartNumber=2, ETag="394b0f6a7c4f393354bb861458652054"
completeMultipart.js:23   Part 3: PartNumber=3, ETag="0d0a10693d4c1fd24e2609697b15500e"
completeMultipart.js:35 ✅ Multipart upload completed successfully!
completeMultipart.js:36 🔗 Final file location: https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753706945438%2Frecording-1753706945438.m4a
completeMultipart.js:37 📝 ETag: "9b7f5fe30e98bce930b9961d592407e7-3"
uploadChunk.js:571 ✅ [COMPLETION] Multipart upload completed successfully on attempt 1
uploadChunk.js:589 🔍 [DEBUG] completeMultipartUpload result: {ServerSideEncryption: 'AES256', Location: 'https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753706945438%2Frecording-1753706945438.m4a', Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753706945438/recording-1753706945438.m4a', ETag: '"9b7f5fe30e98bce930b9961d592407e7-3"', $response: {…}}
uploadChunk.js:594 ✅ Session multipart upload completed successfully!
uploadChunk.js:595 🔗 Final file location: https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753706945438%2Frecording-1753706945438.m4a
uploadChunk.js:596 📝 Final ETag: "9b7f5fe30e98bce930b9961d592407e7-3"
uploadChunk.js:597 📊 Total parts combined: 3
uploadChunk.js:598 📏 Estimated total size: 15.00 MB
uploadChunk.js:602 🔒 [DEBUG] Session data preserved for 1753706945438 (manual cleanup required)
uploadChunk.js:615 🔍 [DEBUG] Final result: {success: true, sessionId: '1753706945438', finalKey: 'recordings/mubarak/1753706945438/recording-1753706945438.m4a', location: 'https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753706945438%2Frecording-1753706945438.m4a', etag: '"9b7f5fe30e98bce930b9961d592407e7-3"', totalParts: 3, totalSize: 15728640, userId: 'mubarak'}
App.tsx:766 ✅ Session multipart upload completed: https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753706945438%2Frecording-1753706945438.m4a
App.tsx:770 📁 Final consolidated file: recordings/mubarak/1753706945438/recording-1753706945438.m4a
App.tsx:773 📊 Total parts combined: 3
App.tsx:776 🔍 [DEBUG] Complete multipart result: {success: true, sessionId: '1753706945438', finalKey: 'recordings/mubarak/1753706945438/recording-1753706945438.m4a', location: 'https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753706945438%2Frecording-1753706945438.m4a', etag: '"9b7f5fe30e98bce930b9961d592407e7-3"', totalParts: 3, totalSize: 15728640, userId: 'mubarak'}
App.tsx:782 🔍 [DEBUG] Uploading multipart manifest...
uploadChunk.js:656 📄 Uploading multipart manifest to recordings/mubarak/1753706945438/manifest.json: {userId: 'mubarak', sessionId: '1753706945438', startTime: '1753706945438', segmentationType: 'multipart-consolidated', targetChunkSize: '5MB+', finalFile: {…}, uploadMethod: 'multipart', createdAt: '2025-07-28T13:04:24.335Z', completedAt: '2025-07-28T13:04:24.335Z'}
uploadChunk.js:667 ✅ Multipart manifest uploaded successfully: recordings/mubarak/1753706945438/manifest.json
App.tsx:788 📋 Multipart manifest uploaded successfully
App.tsx:789 🔍 [DEBUG] Manifest result: {success: true, key: 'recordings/mubarak/1753706945438/manifest.json', etag: '"5ac27825c021bcf5058b46f2007c9e5f"'}
uploadChunk.js:792 🧹 [CLEANUP] Session data cleaned up for 1753706945438
