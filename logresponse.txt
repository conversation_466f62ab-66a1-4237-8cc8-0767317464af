console.js:654 Failed to open debugger. Please check that the dev server is running and reload the app.
anonymous @ console.js:654
overrideMethod @ backend.js:17416
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:96
logIfNoNativeHook @ RCTLog.js:34
Show 6 more frames
Show less
App.tsx:299 📱 Raw onNewAudioChunk event received from iOS: {chunkIndex: 0, fileSize: 5284780, timestamp: 1753462023027, filePath: '/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_220534_001.m4a'}
App.tsx:123 🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
App.tsx:124 ⏰ Event received at: 2025-07-25T16:47:03.143Z
App.tsx:125 📦 Chunk Info: {
  "chunkIndex": 0,
  "fileSize": 5284780,
  "timestamp": 1753462023027,
  "filePath": "/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_220534_001.m4a"
}
App.tsx:126 📦 Current Session ID: 1753461334799
App.tsx:127 📦 Is Recording: true
App.tsx:128 📦 Use Multipart Upload: true
App.tsx:129 🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 0
App.tsx:130 🔍 [FINAL CHUNK] Is final chunk: false
App.tsx:147 ⏱️ [TIMING] Upload started 1ms after event received
App.tsx:152 🚀 [IMMEDIATE UPLOAD] Starting session multipart upload for first chunk (index 0)
App.tsx:153 🔍 [VERIFICATION] This should be the very first chunk of the recording session
uploadChunk.js:57 
� [DEBUG] Starting startSessionMultipartUpload
uploadChunk.js:58 �🚀 Starting session multipart upload for: recordings/mubarak/1753461334799/recording-1753461334799.m4a
uploadChunk.js:59 📋 Session ID: 1753461334799
uploadChunk.js:60 👤 User ID: mubarak
uploadChunk.js:61 🪣 Bucket: audio-recorder-poc
uploadChunk.js:70 🔍 [DEBUG] CreateMultipartUpload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753461334799/recording-1753461334799.m4a', ContentType: 'audio/m4a'}
uploadChunk.js:71 🔍 [DEBUG] Calling s3.createMultipartUpload...
uploadChunk.js:74 🔍 [DEBUG] s3.createMultipartUpload result: {ServerSideEncryption: 'AES256', Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753461334799/recording-1753461334799.m4a', UploadId: 'f7dy4C7siEvjlBLWZ00brmFFE3wdBp3qHKi9_ma.MVrcs5NMp2R32ry74BLIAPftQRgYGXXaRX3WU3dqNMYmu8de7kT4yv16nllnXgFi8OvPLkwTKe_4YMLUBFykTykO', $response: {…}}
uploadChunk.js:85 🔍 [DEBUG] Session data stored: {uploadId: 'f7dy4C7siEvjlBLWZ00brmFFE3wdBp3qHKi9_ma.MVrcs5NMp2R32ry74BLIAPftQRgYGXXaRX3WU3dqNMYmu8de7kT4yv16nllnXgFi8OvPLkwTKe_4YMLUBFykTykO', key: 'recordings/mubarak/1753461334799/recording-1753461334799.m4a', parts: Array(0), userId: 'mubarak'}
uploadChunk.js:86 🔍 [DEBUG] Total active sessions: 1
uploadChunk.js:88 ✅ Session multipart upload started successfully!
uploadChunk.js:89   UploadId: f7dy4C7siEvjlBLWZ00brmFFE3wdBp3qHKi9_ma.MVrcs5NMp2R32ry74BLIAPftQRgYGXXaRX3WU3dqNMYmu8de7kT4yv16nllnXgFi8OvPLkwTKe_4YMLUBFykTykO
uploadChunk.js:90   Key: recordings/mubarak/1753461334799/recording-1753461334799.m4a
uploadChunk.js:91   Bucket: audio-recorder-poc
uploadChunk.js:92   Session: 1753461334799
App.tsx:156 ⏱️ [TIMING] Multipart session started in 313ms
App.tsx:161 🔍 [IMMEDIATE UPLOAD] Uploading chunk 0 as part 1
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753461334799, chunk: 0
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_220534_001.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: YES
uploadChunk.js:310 
🔄 Uploading session chunk 0 (part 1) for session 1753461334799
uploadChunk.js:311 📋 Upload ID: f7dy4C7siEvjlBLWZ00brmFFE3wdBp3qHKi9_ma.MVrcs5NMp2R32ry74BLIAPftQRgYGXXaRX3WU3dqNMYmu8de7kT4yv16nllnXgFi8OvPLkwTKe_4YMLUBFykTykO
uploadChunk.js:312 🔑 Key: recordings/mubarak/1753461334799/recording-1753461334799.m4a
uploadChunk.js:313 🪣 Bucket: audio-recorder-poc
uploadChunk.js:316 🔍 [DEBUG] Reading file: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_220534_001.m4a
App.tsx:312 📱 Removing iOS event listeners
App.tsx:291 📱 Setting up iOS event listener for onNewAudioChunk
App.tsx:292 📱 Current session ID when setting up listener: 1753461334799
App.tsx:293 📱 handleNewAudioChunk function: function
App.tsx:309 📱 Event listener subscriptions created: {chunkSubscription: {…}, testSubscription: {…}}chunkSubscription: remove: ƒ remove()[[Prototype]]: ObjecttestSubscription: remove: ƒ remove()[[Prototype]]: Object[[Prototype]]: Object
uploadChunk.js:320 📁 File read successfully. Size: 5413855 bytes (5.16 MB)
uploadChunk.js:321 🔍 [DEBUG] File buffer created, length: 5413855
uploadChunk.js:337 🔍 [DEBUG] Upload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753461334799/recording-1753461334799.m4a', PartNumber: 1, UploadId: 'f7dy4C7siEvjlBLWZ00brmFFE3wdBp3qHKi9_ma.MVrcs5NMp2R32ry74BLIAPftQRgYGXXaRX3WU3dqNMYmu8de7kT4yv16nllnXgFi8OvPLkwTKe_4YMLUBFykTykO', BodySize: 5413855}
uploadChunk.js:345 🔍 [DEBUG] Calling s3.uploadPart...
uploadChunk.js:347 🔍 [DEBUG] s3.uploadPart result: {ServerSideEncryption: 'AES256', ETag: '"4e309b05d688650f47bbe01dc06c355c"', $response: {…}}ETag: "\"4e309b05d688650f47bbe01dc06c355c\""ServerSideEncryption: "AES256"$response: {request: {…}, data: {…}, error: null, retryCount: 0, redirectCount: 0, httpResponse: {…}, maxRetries: 3, maxRedirects: 10, requestId: 'CJMCKE04QXW8BWAC', extendedRequestId: 'csL4vmxHwKWgZ59xCsTF/ney3er3e9dAWoZvFxb6dOwpNtjyrNbiaO7Nwv4pGiUjKBog50qvcsU=', …}cfId: undefineddata: {ServerSideEncryption: 'AES256', ETag: '"4e309b05d688650f47bbe01dc06c355c"', $response: {…}}error: nullextendedRequestId: "csL4vmxHwKWgZ59xCsTF/ney3er3e9dAWoZvFxb6dOwpNtjyrNbiaO7Nwv4pGiUjKBog50qvcsU="httpResponse: {statusCode: 200, headers: {…}, body: {…}, streaming: false, stream: {…}, statusMessage: undefined, _abortCallback: ƒ}body: {}headers: content-length: "0"date: "Fri, 25 Jul 2025 16:47:09 GMT"etag: "\"4e309b05d688650f47bbe01dc06c355c\""server: "AmazonS3"x-amz-id-2: "csL4vmxHwKWgZ59xCsTF/ney3er3e9dAWoZvFxb6dOwpNtjyrNbiaO7Nwv4pGiUjKBog50qvcsU="x-amz-request-id: "CJMCKE04QXW8BWAC"x-amz-server-side-encryption: "AES256"[[Prototype]]: ObjectstatusCode: 200statusMessage: undefinedstream: {_events: {…}, _maxListeners: undefined, statusCode: 200, headers: {…}}streaming: false_abortCallback: ƒ callNextListener(a0)[[Prototype]]: ObjectmaxRedirects: 10maxRetries: 3redirectCount: 0request: {domain: undefined, service: {…}, operation: 'uploadPart', params: {…}, httpRequest: {…}, startTime: {…}, response: {…}, _asm: {…}, _haltHandlersOnError: false, _events: {…}, …}requestId: "CJMCKE04QXW8BWAC"retryCount: 0[[Prototype]]: Object[[Prototype]]: Object
uploadChunk.js:349 📡 Session chunk 0 upload response: ETag="4e309b05d688650f47bbe01dc06c355c"
uploadChunk.js:355 ✅ SUCCESS: Uploaded session chunk 0 as part 1
uploadChunk.js:356 📦 Total parts uploaded for session: 1
uploadChunk.js:357 🔍 [DEBUG] Current parts: [{…}]0: ETag: "\"4e309b05d688650f47bbe01dc06c355c\""PartNumber: 1[[Prototype]]: Objectlength: 1[[Prototype]]: Array(0)
uploadChunk.js:373 🔍 [DEBUG] Upload result: {success: true, chunkIndex: 0, partNumber: 1, etag: '"4e309b05d688650f47bbe01dc06c355c"', size: 5413855, sessionId: '1753461334799'}chunkIndex: 0etag: "\"4e309b05d688650f47bbe01dc06c355c\""partNumber: 1sessionId: "1753461334799"size: 5413855success: true[[Prototype]]: Object
App.tsx:176 ⏱️ [TIMING] Chunk 0 uploaded in 5037ms
App.tsx:188 ✅ Successfully uploaded multipart chunk 0 (part 1)
App.tsx:222 � Local file preserved: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_220534_001.m4a
App.tsx:223 💾 File size: 5.04 MB
App.tsx:299 📱 Raw onNewAudioChunk event received from iOS: {isFinalChunk: true, fileSize: 1743057, timestamp: 1753462243397, chunkIndex: 1, filePath: '/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_221703_002.m4a'}
App.tsx:123 🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
App.tsx:124 ⏰ Event received at: 2025-07-25T16:50:43.400Z
App.tsx:125 📦 Chunk Info: {
  "isFinalChunk": true,
  "fileSize": 1743057,
  "timestamp": 1753462243397,
  "chunkIndex": 1,
  "filePath": "/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_221703_002.m4a"
}
App.tsx:126 📦 Current Session ID: 1753461334799
App.tsx:127 📦 Is Recording: true
App.tsx:128 📦 Use Multipart Upload: true
App.tsx:129 🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 1
App.tsx:130 🔍 [FINAL CHUNK] Is final chunk: true
App.tsx:133 📦 [FINAL CHUNK] Processing final partial chunk (1.66 MB)
App.tsx:147 ⏱️ [TIMING] Upload started 1ms after event received
App.tsx:161 🔍 [IMMEDIATE UPLOAD] Uploading chunk 1 as part 2
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753461334799, chunk: 1
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_221703_002.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: YES
uploadChunk.js:310 
🔄 Uploading session chunk 1 (part 2) for session 1753461334799
uploadChunk.js:311 📋 Upload ID: f7dy4C7siEvjlBLWZ00brmFFE3wdBp3qHKi9_ma.MVrcs5NMp2R32ry74BLIAPftQRgYGXXaRX3WU3dqNMYmu8de7kT4yv16nllnXgFi8OvPLkwTKe_4YMLUBFykTykO
uploadChunk.js:312 🔑 Key: recordings/mubarak/1753461334799/recording-1753461334799.m4a
uploadChunk.js:313 🪣 Bucket: audio-recorder-poc
uploadChunk.js:316 🔍 [DEBUG] Reading file: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_221703_002.m4a
App.tsx:473 Recording stopped: {success: true}
App.tsx:478 
🔍 [DEBUG] Processing upload completion for session: 1753461334799
App.tsx:479 🔍 [DEBUG] useMultipartUpload: true
App.tsx:480 🔍 [DEBUG] multipartUploadStarted: true
App.tsx:481 🔍 [DEBUG] multipartUploadedChunks.length: 1
App.tsx:482 🔍 [DEBUG] sizeBasedUploadedChunks.length: 0
App.tsx:487 🏁 Completing session multipart upload...
App.tsx:488 📦 Total multipart chunks to combine: 1
App.tsx:489 🔍 [DEBUG] Multipart chunks details: [{…}]
uploadChunk.js:467 
🔍 [DEBUG] Starting finishSessionMultipartUpload for session: 1753461334799
uploadChunk.js:470 🔍 [DEBUG] Session data found: YES
uploadChunk.js:478 🔍 [DEBUG] Session data details: {uploadId: 'f7dy4C7siEvjlBLWZ00brmFFE3wdBp3qHKi9_ma.MVrcs5NMp2R32ry74BLIAPftQRgYGXXaRX3WU3dqNMYmu8de7kT4yv16nllnXgFi8OvPLkwTKe_4YMLUBFykTykO', key: 'recordings/mubarak/1753461334799/recording-1753461334799.m4a', partsCount: 1, userId: 'mubarak'}
uploadChunk.js:490 🏁 Finishing session multipart upload for session 1753461334799
uploadChunk.js:491 📦 Total parts to combine: 1
uploadChunk.js:492 🔑 Final file key: recordings/mubarak/1753461334799/recording-1753461334799.m4a
uploadChunk.js:493 🆔 Upload ID: f7dy4C7siEvjlBLWZ00brmFFE3wdBp3qHKi9_ma.MVrcs5NMp2R32ry74BLIAPftQRgYGXXaRX3WU3dqNMYmu8de7kT4yv16nllnXgFi8OvPLkwTKe_4YMLUBFykTykO
uploadChunk.js:494 🪣 Bucket: audio-recorder-poc
uploadChunk.js:497 🔍 [VALIDATION] Validating parts before completion...
uploadChunk.js:528 ✅ [VALIDATION] All 1 parts validated successfully
uploadChunk.js:529 🔍 [DEBUG] Parts to combine:
uploadChunk.js:531   Part 1: PartNumber=1, ETag="4e309b05d688650f47bbe01dc06c355c"
uploadChunk.js:534 🔍 [DEBUG] Calling completeMultipartUpload with validated parts...
uploadChunk.js:544 🔄 [RETRY] Completion attempt 1/3
completeMultipart.js:13 🔗 Completing multipart upload for recordings/mubarak/1753461334799/recording-1753461334799.m4a
completeMultipart.js:14 📦 Parts to combine: 1
completeMultipart.js:21 📋 Unique parts after deduplication: 1
completeMultipart.js:23   Part 1: PartNumber=1, ETag="4e309b05d688650f47bbe01dc06c355c"
uploadChunk.js:320 📁 File read successfully. Size: 1746853 bytes (1.67 MB)
uploadChunk.js:321 🔍 [DEBUG] File buffer created, length: 1746853
uploadChunk.js:337 🔍 [DEBUG] Upload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753461334799/recording-1753461334799.m4a', PartNumber: 2, UploadId: 'f7dy4C7siEvjlBLWZ00brmFFE3wdBp3qHKi9_ma.MVrcs5NMp2R32ry74BLIAPftQRgYGXXaRX3WU3dqNMYmu8de7kT4yv16nllnXgFi8OvPLkwTKe_4YMLUBFykTykO', BodySize: 1746853}
uploadChunk.js:345 🔍 [DEBUG] Calling s3.uploadPart...
completeMultipart.js:35 ✅ Multipart upload completed successfully!
completeMultipart.js:36 🔗 Final file location: https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753461334799%2Frecording-1753461334799.m4a
completeMultipart.js:37 📝 ETag: "db6a34b866bdc65bfdeb58739e4e1b1c-1"
uploadChunk.js:553 ✅ [COMPLETION] Multipart upload completed successfully on attempt 1
uploadChunk.js:571 🔍 [DEBUG] completeMultipartUpload result: {ServerSideEncryption: 'AES256', Location: 'https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753461334799%2Frecording-1753461334799.m4a', Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753461334799/recording-1753461334799.m4a', ETag: '"db6a34b866bdc65bfdeb58739e4e1b1c-1"', $response: {…}}
uploadChunk.js:576 ✅ Session multipart upload completed successfully!
uploadChunk.js:577 🔗 Final file location: https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753461334799%2Frecording-1753461334799.m4a
uploadChunk.js:578 📝 Final ETag: "db6a34b866bdc65bfdeb58739e4e1b1c-1"
uploadChunk.js:579 📊 Total parts combined: 1
uploadChunk.js:580 📏 Estimated total size: 5.00 MB
uploadChunk.js:584 🧹 [DEBUG] Session data cleaned up for 1753461334799
uploadChunk.js:597 🔍 [DEBUG] Final result: {success: true, sessionId: '1753461334799', finalKey: 'recordings/mubarak/1753461334799/recording-1753461334799.m4a', location: 'https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753461334799%2Frecording-1753461334799.m4a', etag: '"db6a34b866bdc65bfdeb58739e4e1b1c-1"', totalParts: 1, totalSize: 5242880, userId: 'mubarak'}
App.tsx:492 ✅ Session multipart upload completed: https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753461334799%2Frecording-1753461334799.m4a
App.tsx:493 📁 Final consolidated file: recordings/mubarak/1753461334799/recording-1753461334799.m4a
App.tsx:494 📊 Total parts combined: 1
App.tsx:495 🔍 [DEBUG] Complete multipart result: {success: true, sessionId: '1753461334799', finalKey: 'recordings/mubarak/1753461334799/recording-1753461334799.m4a', location: 'https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753461334799%2Frecording-1753461334799.m4a', etag: '"db6a34b866bdc65bfdeb58739e4e1b1c-1"', totalParts: 1, totalSize: 5242880, userId: 'mubarak'}
App.tsx:498 🔍 [DEBUG] Uploading multipart manifest...
uploadChunk.js:638 📄 Uploading multipart manifest to recordings/mubarak/1753461334799/manifest.json: {userId: 'mubarak', sessionId: '1753461334799', startTime: '1753461334799', segmentationType: 'multipart-consolidated', targetChunkSize: '5MB+', finalFile: {…}, uploadMethod: 'multipart', createdAt: '2025-07-25T16:50:44.735Z', completedAt: '2025-07-25T16:50:44.735Z'}
App.tsx:312 📱 Removing iOS event listeners
App.tsx:291 📱 Setting up iOS event listener for onNewAudioChunk
App.tsx:292 📱 Current session ID when setting up listener: 1753461334799
App.tsx:293 📱 handleNewAudioChunk function: function
App.tsx:309 📱 Event listener subscriptions created: {chunkSubscription: {…}, testSubscription: {…}}
uploadChunk.js:649 ✅ Multipart manifest uploaded successfully: recordings/mubarak/1753461334799/manifest.json
App.tsx:500 📋 Multipart manifest uploaded successfully
App.tsx:501 🔍 [DEBUG] Manifest result: {success: true, key: 'recordings/mubarak/1753461334799/manifest.json', etag: '"f0dc90f71968817bc943d42ec0c7ce99"'}
uploadChunk.js:376 ❌ Failed to upload session chunk 1: NoSuchUpload: The specified upload does not exist. The upload ID may be invalid, or the upload may have been aborted or completed.
    at extractError (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:340443:84)
    at apply (native)
    at callListeners (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:112156:31)
    at emit (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:112129:29)
    at call (native)
    at emit (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:118628:26)
    at transition (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:117993:22)
    at call (native)
    at runTo (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:118759:22)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:118768:21)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:118009:21)
    at call (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:118630:24)
    at call (native)
    at callListeners (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:112166:28)
    at emit (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:112129:29)
    at call (native)
    at emit (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:118628:26)
    at transition (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:117993:22)
    at call (native)
    at runTo (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:118759:22)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:118768:21)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:118009:21)
    at call (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:118630:24)
    at call (native)
    at callListeners (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:112166:28)
    at callNextListener (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:112145:31)
    at onEnd (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:115192:25)
    at call (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:140536:23)
    at finishRequest (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:140233:19)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:140153:29)
    at call (native)
    at invoke (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:45126:24)
    at dispatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:45084:13)
    at value (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:45051:17)
    at dispatchTrustedEvent (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:45172:53)
    at setReadyState (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:47025:137)
    at __didCompleteResponse (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:46841:29)
    at apply (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:46973:52)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:2169:40)
    at apply (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:2030:200)
    at emit (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:2046:66)
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:376
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 8 more frames
Show less
uploadChunk.js:377 🔍 [DEBUG] Error details: {name: 'NoSuchUpload', message: 'The specified upload does not exist. The upload ID may be invalid, or the upload may have been aborted or completed.', code: 'NoSuchUpload', statusCode: 404}
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:377
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 8 more frames
Show less
uploadChunk.js:830 Upload attempt 1 failed, retrying in 1000ms: The specified upload does not exist. The upload ID may be invalid, or the upload may have been aborted or completed.
anonymous @ console.js:654
overrideMethod @ backend.js:17416
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
?anon_0__loop2 @ uploadChunk.js:830
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 7 more frames
Show less
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753461334799, chunk: 1
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_221703_002.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: NO
uploadChunk.js:302 ❌ [DEBUG] No session data found for 1753461334799
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:302
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:303 🔍 [DEBUG] Available sessions: []
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:303
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:376 ❌ Failed to upload session chunk 1: Error: No active multipart upload for session 1753461334799. Call startSessionMultipartUpload() first.
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:105710:26)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22563:14)
    at tryCallTwo (address at InternalBytecode.js:1:1222)
    at doResolve (address at InternalBytecode.js:1:2541)
    at Promise (address at InternalBytecode.js:1:1318)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22555:25)
    at apply (native)
    at uploadSessionChunk (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:105688:37)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:103587:108)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22563:14)
    at tryCallTwo (address at InternalBytecode.js:1:1222)
    at doResolve (address at InternalBytecode.js:1:2541)
    at Promise (address at InternalBytecode.js:1:1318)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22555:25)
    at ?anon_0__loop2 (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:106252:32)
    at next (native)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:106269:16)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:376
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:377 🔍 [DEBUG] Error details: {name: 'Error', message: 'No active multipart upload for session 1753461334799. Call startSessionMultipartUpload() first.', code: undefined, statusCode: undefined}
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:377
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:830 Upload attempt 2 failed, retrying in 2000ms: No active multipart upload for session 1753461334799. Call startSessionMultipartUpload() first.
anonymous @ console.js:654
overrideMethod @ backend.js:17416
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
?anon_0__loop2 @ uploadChunk.js:830
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 7 more frames
Show less
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753461334799, chunk: 1
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_221703_002.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: NO
uploadChunk.js:302 ❌ [DEBUG] No session data found for 1753461334799
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:302
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:303 🔍 [DEBUG] Available sessions: []
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:303
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:376 ❌ Failed to upload session chunk 1: Error: No active multipart upload for session 1753461334799. Call startSessionMultipartUpload() first.
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:105710:26)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22563:14)
    at tryCallTwo (address at InternalBytecode.js:1:1222)
    at doResolve (address at InternalBytecode.js:1:2541)
    at Promise (address at InternalBytecode.js:1:1318)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22555:25)
    at apply (native)
    at uploadSessionChunk (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:105688:37)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:103587:108)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22563:14)
    at tryCallTwo (address at InternalBytecode.js:1:1222)
    at doResolve (address at InternalBytecode.js:1:2541)
    at Promise (address at InternalBytecode.js:1:1318)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22555:25)
    at ?anon_0__loop2 (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:106252:32)
    at next (native)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:106269:16)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:376
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:377 🔍 [DEBUG] Error details: {name: 'Error', message: 'No active multipart upload for session 1753461334799. Call startSessionMultipartUpload() first.', code: undefined, statusCode: undefined}
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:377
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:830 Upload attempt 3 failed, retrying in 4000ms: No active multipart upload for session 1753461334799. Call startSessionMultipartUpload() first.
anonymous @ console.js:654
overrideMethod @ backend.js:17416
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
?anon_0__loop2 @ uploadChunk.js:830
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 7 more frames
Show less
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753461334799, chunk: 1
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/BEB3D973-CBDB-4B5C-9DB5-37A1BA822B26/Documents/recording_20250725_221703_002.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: NO
uploadChunk.js:302 ❌ [DEBUG] No session data found for 1753461334799
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:302
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:303 🔍 [DEBUG] Available sessions: []
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:303
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:376 ❌ Failed to upload session chunk 1: Error: No active multipart upload for session 1753461334799. Call startSessionMultipartUpload() first.
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:105710:26)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22563:14)
    at tryCallTwo (address at InternalBytecode.js:1:1222)
    at doResolve (address at InternalBytecode.js:1:2541)
    at Promise (address at InternalBytecode.js:1:1318)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22555:25)
    at apply (native)
    at uploadSessionChunk (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:105688:37)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:103587:108)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22563:14)
    at tryCallTwo (address at InternalBytecode.js:1:1222)
    at doResolve (address at InternalBytecode.js:1:2541)
    at Promise (address at InternalBytecode.js:1:1318)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22555:25)
    at ?anon_0__loop2 (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:106252:32)
    at next (native)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:106269:16)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:376
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:377 🔍 [DEBUG] Error details: {name: 'Error', message: 'No active multipart upload for session 1753461334799. Call startSessionMultipartUpload() first.', code: undefined, statusCode: undefined}
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ uploadChunk.js:377
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
uploadSessionChunk @ uploadChunk.js:293
?anon_0_ @ App.tsx:165
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
?anon_0__loop2 @ uploadChunk.js:820
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 16 more frames
Show less
uploadChunk.js:825 Upload failed after 4 attempts: Error: No active multipart upload for session 1753461334799. Call startSessionMultipartUpload() first.
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:105710:26)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22563:14)
    at tryCallTwo (address at InternalBytecode.js:1:1222)
    at doResolve (address at InternalBytecode.js:1:2541)
    at Promise (address at InternalBytecode.js:1:1318)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22555:25)
    at apply (native)
    at uploadSessionChunk (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:105688:37)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:103587:108)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22563:14)
    at tryCallTwo (address at InternalBytecode.js:1:1222)
    at doResolve (address at InternalBytecode.js:1:2541)
    at Promise (address at InternalBytecode.js:1:1318)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22555:25)
    at ?anon_0__loop2 (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:106252:32)
    at next (native)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:106269:16)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0__loop2 @ uploadChunk.js:825
?anon_0_ @ uploadChunk.js:818
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 8 more frames
Show less
App.tsx:240 ❌ Failed to upload chunk 1: Error: No active multipart upload for session 1753461334799. Call startSessionMultipartUpload() first.
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:105710:26)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22563:14)
    at tryCallTwo (address at InternalBytecode.js:1:1222)
    at doResolve (address at InternalBytecode.js:1:2541)
    at Promise (address at InternalBytecode.js:1:1318)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22555:25)
    at apply (native)
    at uploadSessionChunk (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:105688:37)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:103587:108)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22563:14)
    at tryCallTwo (address at InternalBytecode.js:1:1222)
    at doResolve (address at InternalBytecode.js:1:2541)
    at Promise (address at InternalBytecode.js:1:1318)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22555:25)
    at ?anon_0__loop2 (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:106252:32)
    at next (native)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:106269:16)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22544:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:22558:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ App.tsx:240
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 8 more frames
Show less
console.js:654 Warning: ReferenceError: Property 'error' doesn't exist

This error is located at:
    at App (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:103456:54)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:5624:43)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:5624:43)
    at AppContainer (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:51724:25)
    at AlwaysOnRecorder(RootComponent) (http://localhost:8081/index.bundle//&platform=ios&dev=true&lazy=true&minify=false&inlineSourceMap=false&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server&app=org.reactjs.native.example.AlwaysOnRecorder:68588:28), js engine: hermes
anonymous @ console.js:654
overrideMethod @ backend.js:17416
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:260
anonymous @ LogBox.js:80
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
onUncaughtError @ ErrorHandlers.js:63
logUncaughtError @ ReactFabric-dev.js:6747
runWithFiberInDEV @ ReactFabric-dev.js:683
anonymous @ ReactFabric-dev.js:6777
callCallback @ ReactFabric-dev.js:3691
commitCallbacks @ ReactFabric-dev.js:3711
runWithFiberInDEV @ ReactFabric-dev.js:683
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10231
flushLayoutEffects @ ReactFabric-dev.js:12545
commitRoot @ ReactFabric-dev.js:12488
commitRootWhenReady @ ReactFabric-dev.js:11738
performWorkOnRoot @ ReactFabric-dev.js:11700
performWorkOnRootViaSchedulerTask @ ReactFabric-dev.js:3073
Show 21 more frames
Show less
App.tsx:312 📱 Removing iOS event listeners
