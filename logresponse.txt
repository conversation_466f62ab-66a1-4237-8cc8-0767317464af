Welcome to React Native DevTools
Debugger integration: iOS Bridgeless (RCTHost)
console.js:654 Failed to open debugger. Please check that the dev server is running and reload the app.
anonymous @ console.js:654
overrideMethod @ backend.js:17416
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:96
logIfNoNativeHook @ RCTLog.js:34
Show 6 more frames
Show less
console.js:654 Failed to open debugger. Please check that the dev server is running and reload the app.
anonymous @ console.js:654
overrideMethod @ backend.js:17416
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:96
logIfNoNativeHook @ RCTLog.js:34
Show 6 more frames
Show less
App.tsx:451 📱 Raw onNewAudioChunk event received from iOS: {fileSize: 5364701, timestamp: 1753705842803, chunkIndex: 0, hasMovAtom: true, filePath: '/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_175507_001.m4a'}
App.tsx:177 🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
App.tsx:178 ⏰ Event received at: 2025-07-28T12:30:42.805Z
App.tsx:181 📦 Chunk Info: {
  "fileSize": 5364701,
  "timestamp": 1753705842803,
  "chunkIndex": 0,
  "hasMovAtom": true,
  "filePath": "/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_175507_001.m4a"
}
App.tsx:182 📦 Current Session ID: 1753705507611
App.tsx:183 📦 Is Recording: true
App.tsx:184 📦 Use Multipart Upload: true
App.tsx:185 🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 0
App.tsx:188 🔍 [FINAL CHUNK] Is final chunk: false
App.tsx:214 ⏱️ [TIMING] Upload started 1ms after event received
App.tsx:223 🚀 [IMMEDIATE UPLOAD] Starting session multipart upload for first chunk (index 0)
App.tsx:226 🔍 [VERIFICATION] This should be the very first chunk of the recording session
uploadChunk.js:57 
� [DEBUG] Starting startSessionMultipartUpload
uploadChunk.js:58 �🚀 Starting session multipart upload for: recordings/mubarak/1753705507611/recording-1753705507611.m4a
uploadChunk.js:59 📋 Session ID: 1753705507611
uploadChunk.js:60 👤 User ID: mubarak
uploadChunk.js:61 🪣 Bucket: audio-recorder-poc
uploadChunk.js:70 🔍 [DEBUG] CreateMultipartUpload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753705507611/recording-1753705507611.m4a', ContentType: 'audio/m4a'}
uploadChunk.js:71 🔍 [DEBUG] Calling s3.createMultipartUpload...
uploadChunk.js:74 🔍 [DEBUG] s3.createMultipartUpload result: {ServerSideEncryption: 'AES256', Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753705507611/recording-1753705507611.m4a', UploadId: 'ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry', $response: {…}}
uploadChunk.js:85 🔍 [DEBUG] Session data stored: {uploadId: 'ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry', key: 'recordings/mubarak/1753705507611/recording-1753705507611.m4a', parts: Array(0), userId: 'mubarak'}
uploadChunk.js:86 🔍 [DEBUG] Total active sessions: 1
uploadChunk.js:88 ✅ Session multipart upload started successfully!
uploadChunk.js:89   UploadId: ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry
uploadChunk.js:90   Key: recordings/mubarak/1753705507611/recording-1753705507611.m4a
uploadChunk.js:91   Bucket: audio-recorder-poc
uploadChunk.js:92   Session: 1753705507611
App.tsx:234 ⏱️ [TIMING] Multipart session started in 188ms
App.tsx:243 🔍 [IMMEDIATE UPLOAD] Uploading chunk 0 as part 1
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753705507611, chunk: 0
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_175507_001.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: YES
uploadChunk.js:310 
🔄 Uploading session chunk 0 (part 1) for session 1753705507611
uploadChunk.js:311 📋 Upload ID: ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry
uploadChunk.js:312 🔑 Key: recordings/mubarak/1753705507611/recording-1753705507611.m4a
uploadChunk.js:313 🪣 Bucket: audio-recorder-poc
uploadChunk.js:316 🔍 [DEBUG] Reading file: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_175507_001.m4a
App.tsx:474 📱 Removing iOS event listeners
App.tsx:433 📱 Setting up iOS event listener for onNewAudioChunk
App.tsx:434 📱 Current session ID when setting up listener: 1753705507611
App.tsx:438 📱 handleNewAudioChunk function: function
App.tsx:468 📱 Event listener subscriptions created: {chunkSubscription: {…}, testSubscription: {…}}
uploadChunk.js:320 📁 File read successfully. Size: 5364701 bytes (5.12 MB)
uploadChunk.js:321 🔍 [DEBUG] File buffer created, length: 5364701
uploadChunk.js:337 🔍 [DEBUG] Upload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753705507611/recording-1753705507611.m4a', PartNumber: 1, UploadId: 'ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry', BodySize: 5364701}BodySize: 5364701Bucket: "audio-recorder-poc"Key: "recordings/mubarak/1753705507611/recording-1753705507611.m4a"PartNumber: 1UploadId: "ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry"[[Prototype]]: Object
uploadChunk.js:345 🔍 [DEBUG] Calling s3.uploadPart...
uploadChunk.js:347 🔍 [DEBUG] s3.uploadPart result: {ServerSideEncryption: 'AES256', ETag: '"2349de0ef3dc6b14c95cf6fe162bf3d2"', $response: {…}}ETag: "\"2349de0ef3dc6b14c95cf6fe162bf3d2\""ServerSideEncryption: "AES256"$response: {request: {…}, data: {…}, error: null, retryCount: 0, redirectCount: 0, httpResponse: {…}, maxRetries: 3, maxRedirects: 10, requestId: 'DVYEME7ZGGW7BQY2', extendedRequestId: 'HlTrRnAhIFwU7T47Qj0VtbBBBLHC8ie5/RCYavPQGVN3TaPQXCJZ6ID08pUOkNSPzlZpN9+IlXg=', …}[[Prototype]]: Object
uploadChunk.js:349 📡 Session chunk 0 upload response: ETag="2349de0ef3dc6b14c95cf6fe162bf3d2"
uploadChunk.js:355 ✅ SUCCESS: Uploaded session chunk 0 as part 1
uploadChunk.js:356 📦 Total parts uploaded for session: 1
uploadChunk.js:357 🔍 [DEBUG] Current parts: [{…}]
uploadChunk.js:373 🔍 [DEBUG] Upload result: {success: true, chunkIndex: 0, partNumber: 1, etag: '"2349de0ef3dc6b14c95cf6fe162bf3d2"', size: 5364701, sessionId: '1753705507611'}
App.tsx:267 ⏱️ [TIMING] Chunk 0 uploaded in 4746ms
App.tsx:299 ✅ Successfully uploaded multipart chunk 0 (part 1)
App.tsx:352 � Local file preserved: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_175507_001.m4a
App.tsx:353 💾 File size: 5.12 MB
App.tsx:451 📱 Raw onNewAudioChunk event received from iOS: {fileSize: 5362722, timestamp: 1753706177800, hasMovAtom: true, chunkIndex: 1, filePath: '/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_180042_002.m4a'}
App.tsx:177 🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
App.tsx:178 ⏰ Event received at: 2025-07-28T12:36:17.801Z
App.tsx:181 📦 Chunk Info: {
  "fileSize": 5362722,
  "timestamp": 1753706177800,
  "hasMovAtom": true,
  "chunkIndex": 1,
  "filePath": "/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_180042_002.m4a"
}
App.tsx:182 📦 Current Session ID: 1753705507611
App.tsx:183 📦 Is Recording: true
App.tsx:184 📦 Use Multipart Upload: true
App.tsx:185 🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 1
App.tsx:188 🔍 [FINAL CHUNK] Is final chunk: false
App.tsx:214 ⏱️ [TIMING] Upload started 2ms after event received
App.tsx:243 🔍 [IMMEDIATE UPLOAD] Uploading chunk 1 as part 2
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753705507611, chunk: 1
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_180042_002.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: YES
uploadChunk.js:310 
🔄 Uploading session chunk 1 (part 2) for session 1753705507611
uploadChunk.js:311 📋 Upload ID: ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry
uploadChunk.js:312 🔑 Key: recordings/mubarak/1753705507611/recording-1753705507611.m4a
uploadChunk.js:313 🪣 Bucket: audio-recorder-poc
uploadChunk.js:316 🔍 [DEBUG] Reading file: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_180042_002.m4a
uploadChunk.js:320 📁 File read successfully. Size: 5362722 bytes (5.11 MB)
uploadChunk.js:321 🔍 [DEBUG] File buffer created, length: 5362722
uploadChunk.js:337 🔍 [DEBUG] Upload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753705507611/recording-1753705507611.m4a', PartNumber: 2, UploadId: 'ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry', BodySize: 5362722}
uploadChunk.js:345 🔍 [DEBUG] Calling s3.uploadPart...
uploadChunk.js:347 🔍 [DEBUG] s3.uploadPart result: {ServerSideEncryption: 'AES256', ETag: '"048e798b6e7273a0d670fd3170687ab8"', $response: {…}}
uploadChunk.js:349 📡 Session chunk 1 upload response: ETag="048e798b6e7273a0d670fd3170687ab8"
uploadChunk.js:355 ✅ SUCCESS: Uploaded session chunk 1 as part 2
uploadChunk.js:356 📦 Total parts uploaded for session: 2
uploadChunk.js:357 🔍 [DEBUG] Current parts: (2) [{…}, {…}]
uploadChunk.js:373 🔍 [DEBUG] Upload result: {success: true, chunkIndex: 1, partNumber: 2, etag: '"048e798b6e7273a0d670fd3170687ab8"', size: 5362722, sessionId: '1753705507611'}chunkIndex: 1etag: "\"048e798b6e7273a0d670fd3170687ab8\""partNumber: 2sessionId: "1753705507611"size: 5362722success: true[[Prototype]]: Object
App.tsx:267 ⏱️ [TIMING] Chunk 1 uploaded in 4743ms
App.tsx:299 ✅ Successfully uploaded multipart chunk 1 (part 2)
App.tsx:352 � Local file preserved: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_180042_002.m4a
App.tsx:353 💾 File size: 5.11 MB
App.tsx:665 Recording stopped: {success: true}
App.tsx:669 ⏳ [RACE CONDITION FIX] Waiting for pending uploads to complete...
App.tsx:672 🔍 [RACE CONDITION FIX] Final chunk received: false
App.tsx:451 📱 Raw onNewAudioChunk event received from iOS: {isFinalChunk: true, timestamp: 1753706305684, chunkIndex: 2, fileSize: 2063032, filePath: '/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_180617_003.m4a', hasMovAtom: true}
App.tsx:177 🎉 ===== NEW AUDIO CHUNK EVENT RECEIVED =====
App.tsx:178 ⏰ Event received at: 2025-07-28T12:38:25.686Z
App.tsx:181 📦 Chunk Info: {
  "isFinalChunk": true,
  "timestamp": 1753706305684,
  "chunkIndex": 2,
  "fileSize": 2063032,
  "filePath": "/Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_180617_003.m4a",
  "hasMovAtom": true
}
App.tsx:182 📦 Current Session ID: 1753705507611
App.tsx:183 📦 Is Recording: true
App.tsx:184 📦 Use Multipart Upload: true
App.tsx:185 🔍 [IMMEDIATE UPLOAD] Starting immediate upload for chunk 2
App.tsx:188 🔍 [FINAL CHUNK] Is final chunk: true
App.tsx:193 📦 [FINAL CHUNK] Processing final partial chunk (1.97 MB)
App.tsx:214 ⏱️ [TIMING] Upload started 3ms after event received
App.tsx:243 🔍 [IMMEDIATE UPLOAD] Uploading chunk 2 as part 3
uploadChunk.js:295 
🔍 [DEBUG] Starting uploadSessionChunk for session: 1753705507611, chunk: 2
uploadChunk.js:296 🔍 [DEBUG] Local file URI: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_180617_003.m4a
uploadChunk.js:299 🔍 [DEBUG] Session data found: YES
uploadChunk.js:310 
🔄 Uploading session chunk 2 (part 3) for session 1753705507611
uploadChunk.js:311 📋 Upload ID: ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry
uploadChunk.js:312 🔑 Key: recordings/mubarak/1753705507611/recording-1753705507611.m4a
uploadChunk.js:313 🪣 Bucket: audio-recorder-poc
uploadChunk.js:316 🔍 [DEBUG] Reading file: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_180617_003.m4a
uploadChunk.js:320 📁 File read successfully. Size: 2063032 bytes (1.97 MB)
uploadChunk.js:321 🔍 [DEBUG] File buffer created, length: 2063032
uploadChunk.js:337 🔍 [DEBUG] Upload params: {Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753705507611/recording-1753705507611.m4a', PartNumber: 3, UploadId: 'ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry', BodySize: 2063032}
uploadChunk.js:345 🔍 [DEBUG] Calling s3.uploadPart...
App.tsx:474 📱 Removing iOS event listeners
App.tsx:433 📱 Setting up iOS event listener for onNewAudioChunk
App.tsx:434 📱 Current session ID when setting up listener: 1753705507611
App.tsx:438 📱 handleNewAudioChunk function: function
App.tsx:468 📱 Event listener subscriptions created: {chunkSubscription: {…}, testSubscription: {…}}
App.tsx:710 ⏳ [RACE CONDITION FIX] Waiting for 2 remaining uploads to finish...
uploadChunk.js:347 🔍 [DEBUG] s3.uploadPart result: {ServerSideEncryption: 'AES256', ETag: '"95190b1f0b9fad180f8725f0ed099fb0"', $response: {…}}
uploadChunk.js:349 📡 Session chunk 2 upload response: ETag="95190b1f0b9fad180f8725f0ed099fb0"
uploadChunk.js:355 ✅ SUCCESS: Uploaded session chunk 2 as part 3
uploadChunk.js:356 📦 Total parts uploaded for session: 3
uploadChunk.js:357 🔍 [DEBUG] Current parts: (3) [{…}, {…}, {…}]
uploadChunk.js:373 🔍 [DEBUG] Upload result: {success: true, chunkIndex: 2, partNumber: 3, etag: '"95190b1f0b9fad180f8725f0ed099fb0"', size: 2063032, sessionId: '1753705507611'}
App.tsx:267 ⏱️ [TIMING] Chunk 2 uploaded in 1994ms
App.tsx:299 ✅ Successfully uploaded multipart chunk 2 (part 3)
App.tsx:352 � Local file preserved: /Users/<USER>/Library/Developer/CoreSimulator/Devices/8DC7ADF9-B92E-4741-BF89-2E8CA49FFBA8/data/Containers/Data/Application/F53EBFC7-1D2D-452B-B78F-622A7E414CAE/Documents/recording_20250728_180617_003.m4a
App.tsx:353 💾 File size: 1.97 MB
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:723 ⏳ [RACE CONDITION FIX] Still waiting... 2 uploads remaining
App.tsx:733 
🔍 [DEBUG] Processing upload completion for session: 1753705507611
App.tsx:736 🔍 [DEBUG] useMultipartUpload: true
App.tsx:737 🔍 [DEBUG] multipartUploadStarted: true
App.tsx:740 🔍 [DEBUG] multipartUploadedChunks.length: 2
App.tsx:743 🔍 [DEBUG] sizeBasedUploadedChunks.length: 0
App.tsx:754 🏁 Completing session multipart upload...
App.tsx:755 📦 Total multipart chunks to combine: 2
App.tsx:758 🔍 [DEBUG] Multipart chunks details: (2) [{…}, {…}]
uploadChunk.js:485 
🔍 [DEBUG] Starting finishSessionMultipartUpload for session: 1753705507611
uploadChunk.js:488 🔍 [DEBUG] Session data found: YES
uploadChunk.js:496 🔍 [DEBUG] Session data details: {uploadId: 'ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry', key: 'recordings/mubarak/1753705507611/recording-1753705507611.m4a', partsCount: 3, userId: 'mubarak'}
uploadChunk.js:508 🏁 Finishing session multipart upload for session 1753705507611
uploadChunk.js:509 📦 Total parts to combine: 3
uploadChunk.js:510 🔑 Final file key: recordings/mubarak/1753705507611/recording-1753705507611.m4a
uploadChunk.js:511 🆔 Upload ID: ZoJNL21ExJWU3MxH0W1mc62S9GGEHNgakR_FV_BrsMZnzM9hhkjDfyy8cj9YWiNBEp_li8bViXeVhX3WphZZ9ZseQu4HXybgfh6246CknBruUh6zEzn0BO_GwFtcZfry
uploadChunk.js:512 🪣 Bucket: audio-recorder-poc
uploadChunk.js:515 🔍 [VALIDATION] Validating parts before completion...
uploadChunk.js:546 ✅ [VALIDATION] All 3 parts validated successfully
uploadChunk.js:547 🔍 [DEBUG] Parts to combine:
uploadChunk.js:549   Part 1: PartNumber=1, ETag="2349de0ef3dc6b14c95cf6fe162bf3d2"
uploadChunk.js:549   Part 2: PartNumber=2, ETag="048e798b6e7273a0d670fd3170687ab8"
uploadChunk.js:549   Part 3: PartNumber=3, ETag="95190b1f0b9fad180f8725f0ed099fb0"
uploadChunk.js:552 🔍 [DEBUG] Calling completeMultipartUpload with validated parts...
uploadChunk.js:562 🔄 [RETRY] Completion attempt 1/3
completeMultipart.js:13 🔗 Completing multipart upload for recordings/mubarak/1753705507611/recording-1753705507611.m4a
completeMultipart.js:14 📦 Parts to combine: 3
completeMultipart.js:21 📋 Unique parts after deduplication: 3
completeMultipart.js:23   Part 1: PartNumber=1, ETag="2349de0ef3dc6b14c95cf6fe162bf3d2"
completeMultipart.js:23   Part 2: PartNumber=2, ETag="048e798b6e7273a0d670fd3170687ab8"
completeMultipart.js:23   Part 3: PartNumber=3, ETag="95190b1f0b9fad180f8725f0ed099fb0"
completeMultipart.js:35 ✅ Multipart upload completed successfully!
completeMultipart.js:36 🔗 Final file location: https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753705507611%2Frecording-1753705507611.m4a
completeMultipart.js:37 📝 ETag: "a4523375edacc3fc2662f751c77ca5c7-3"
uploadChunk.js:571 ✅ [COMPLETION] Multipart upload completed successfully on attempt 1
uploadChunk.js:589 🔍 [DEBUG] completeMultipartUpload result: {ServerSideEncryption: 'AES256', Location: 'https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753705507611%2Frecording-1753705507611.m4a', Bucket: 'audio-recorder-poc', Key: 'recordings/mubarak/1753705507611/recording-1753705507611.m4a', ETag: '"a4523375edacc3fc2662f751c77ca5c7-3"', $response: {…}}
uploadChunk.js:594 ✅ Session multipart upload completed successfully!
uploadChunk.js:595 🔗 Final file location: https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753705507611%2Frecording-1753705507611.m4a
uploadChunk.js:596 📝 Final ETag: "a4523375edacc3fc2662f751c77ca5c7-3"
uploadChunk.js:597 📊 Total parts combined: 3
uploadChunk.js:598 📏 Estimated total size: 15.00 MB
uploadChunk.js:602 🔒 [DEBUG] Session data preserved for 1753705507611 (manual cleanup required)
uploadChunk.js:615 🔍 [DEBUG] Final result: {success: true, sessionId: '1753705507611', finalKey: 'recordings/mubarak/1753705507611/recording-1753705507611.m4a', location: 'https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753705507611%2Frecording-1753705507611.m4a', etag: '"a4523375edacc3fc2662f751c77ca5c7-3"', totalParts: 3, totalSize: 15728640, userId: 'mubarak'}
App.tsx:766 ✅ Session multipart upload completed: https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753705507611%2Frecording-1753705507611.m4a
App.tsx:770 📁 Final consolidated file: recordings/mubarak/1753705507611/recording-1753705507611.m4a
App.tsx:773 📊 Total parts combined: 3
App.tsx:776 🔍 [DEBUG] Complete multipart result: {success: true, sessionId: '1753705507611', finalKey: 'recordings/mubarak/1753705507611/recording-1753705507611.m4a', location: 'https://audio-recorder-poc.s3.ap-south-1.amazonaws.com/recordings%2Fmubarak%2F1753705507611%2Frecording-1753705507611.m4a', etag: '"a4523375edacc3fc2662f751c77ca5c7-3"', totalParts: 3, totalSize: 15728640, userId: 'mubarak'}
App.tsx:782 🔍 [DEBUG] Uploading multipart manifest...
uploadChunk.js:656 📄 Uploading multipart manifest to recordings/mubarak/1753705507611/manifest.json: {userId: 'mubarak', sessionId: '1753705507611', startTime: '1753705507611', segmentationType: 'multipart-consolidated', targetChunkSize: '5MB+', finalFile: {…}, uploadMethod: 'multipart', createdAt: '2025-07-28T12:38:37.683Z', completedAt: '2025-07-28T12:38:37.683Z'}
uploadChunk.js:667 ✅ Multipart manifest uploaded successfully: recordings/mubarak/1753705507611/manifest.json
App.tsx:788 📋 Multipart manifest uploaded successfully
App.tsx:789 🔍 [DEBUG] Manifest result: {success: true, key: 'recordings/mubarak/1753705507611/manifest.json', etag: '"8d57162a0f883ab2cc323d43413fe943"'}
uploadChunk.js:792 🧹 [CLEANUP] Session data cleaned up for 1753705507611
