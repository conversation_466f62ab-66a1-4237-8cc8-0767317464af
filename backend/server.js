// Simple Express server for generating S3 presigned URLs
const express = require('express');
const AWS = require('aws-sdk');
const cors = require('cors');

const app = express();
const port = 3001;

// Configure AWS - Use environment variables for security
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID || '********************',
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'LOcgG9+2wJ2vYhghrOwvkcmm8S+AZ8saWfwqnwx1',
  region: process.env.AWS_REGION || 'ap-south-1'
});

console.log('AWS Config:', {
  accessKeyId: AWS.config.credentials?.accessKeyId || 'Not set',
  region: AWS.config.region,
  hasSecretKey: !!AWS.config.credentials?.secretAccessKey
});

const s3 = new AWS.S3();

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Generate presigned URL for S3 upload
app.post('/presign', async (req, res) => {
  try {
    const { key, contentType = 'audio/m4a' } = req.body;

    if (!key) {
      return res.status(400).json({ error: 'Key is required' });
    }

    console.log(`Generating presigned URL for key: ${key}`);

    const params = {
      Bucket: 'audio-recorder-poc',
      Key: key,
      Expires: 300, // 5 minutes
      ContentType: contentType,
      ACL: 'private'
    };

    const url = await s3.getSignedUrlPromise('putObject', params);

    console.log(`Generated presigned URL: ${url.substring(0, 100)}...`);

    res.json({ 
      url,
      expires: new Date(Date.now() + 300000).toISOString() // 5 minutes from now
    });

  } catch (error) {
    console.error('Error generating presigned URL:', error);
    res.status(500).json({ 
      error: 'Failed to generate presigned URL',
      details: error.message 
    });
  }
});

// Start server
app.listen(port, () => {
  console.log(`Presigned URL server running at http://localhost:${port}`);
  console.log(`Health check: http://localhost:${port}/health`);
  console.log(`Presign endpoint: http://localhost:${port}/presign`);
});
